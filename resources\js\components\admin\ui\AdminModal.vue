<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="modelValue"
        class="fixed inset-0 z-50 overflow-y-auto"
        @click="handleBackdropClick"
      >
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" />
        
        <!-- Modal Container -->
        <div class="flex min-h-full items-center justify-center p-4">
          <Transition
            enter-active-class="transition duration-300 ease-out"
            enter-from-class="opacity-0 scale-95"
            enter-to-class="opacity-100 scale-100"
            leave-active-class="transition duration-200 ease-in"
            leave-from-class="opacity-100 scale-100"
            leave-to-class="opacity-0 scale-95"
          >
            <div
              v-if="modelValue"
              :class="modalClasses"
              @click.stop
            >
              <!-- Modal Header -->
              <div v-if="showHeader" :class="headerClasses">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <!-- Icon -->
                    <div v-if="icon" :class="iconClasses">
                      <component :is="iconComponent" class="w-5 h-5 text-white" />
                    </div>
                    
                    <!-- Title -->
                    <div>
                      <h3 v-if="title" class="text-lg font-medium text-gray-900">
                        {{ title }}
                      </h3>
                      <p v-if="subtitle" class="text-sm text-gray-500">
                        {{ subtitle }}
                      </p>
                    </div>
                  </div>
                  
                  <!-- Close Button -->
                  <button
                    v-if="closable"
                    @click="close"
                    class="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <!-- Header Slot -->
                <slot name="header" />
              </div>

              <!-- Modal Body -->
              <div :class="bodyClasses">
                <slot />
              </div>

              <!-- Modal Footer -->
              <div v-if="showFooter" :class="footerClasses">
                <slot name="footer">
                  <!-- Default Footer Actions -->
                  <div class="flex justify-end space-x-3">
                    <Button
                      v-if="showCancel"
                      variant="outline"
                      @click="cancel"
                      :disabled="loading"
                    >
                      {{ cancelText }}
                    </Button>
                    <Button
                      v-if="showConfirm"
                      :variant="confirmVariant"
                      @click="confirm"
                      :loading="loading"
                    >
                      {{ confirmText }}
                    </Button>
                  </div>
                </slot>
              </div>
            </div>
          </Transition>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue';
import { Button } from '@/components/ui';

interface Props {
  modelValue: boolean;
  title?: string;
  subtitle?: string;
  icon?: 'info' | 'warning' | 'error' | 'success' | 'question';
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  variant?: 'default' | 'danger' | 'warning' | 'success';
  closable?: boolean;
  closeOnBackdrop?: boolean;
  closeOnEscape?: boolean;
  showHeader?: boolean;
  showFooter?: boolean;
  showCancel?: boolean;
  showConfirm?: boolean;
  cancelText?: string;
  confirmText?: string;
  confirmVariant?: 'primary' | 'danger' | 'warning' | 'success';
  loading?: boolean;
  persistent?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'default',
  closable: true,
  closeOnBackdrop: true,
  closeOnEscape: true,
  showHeader: true,
  showFooter: false,
  showCancel: true,
  showConfirm: true,
  cancelText: 'Cancel',
  confirmText: 'Confirm',
  confirmVariant: 'primary',
  loading: false,
  persistent: false
});

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  close: [];
  cancel: [];
  confirm: [];
}>();

// Computed
const modalClasses = computed(() => {
  const baseClasses = 'relative bg-white rounded-lg shadow-xl transform transition-all';
  
  const sizeClasses = {
    sm: 'max-w-sm w-full',
    md: 'max-w-md w-full',
    lg: 'max-w-lg w-full',
    xl: 'max-w-xl w-full',
    full: 'max-w-4xl w-full mx-4'
  };
  
  return `${baseClasses} ${sizeClasses[props.size]}`;
});

const headerClasses = computed(() => {
  const baseClasses = 'px-6 py-4 border-b border-gray-200';
  
  const variantClasses = {
    default: '',
    danger: 'bg-red-50 border-red-200',
    warning: 'bg-yellow-50 border-yellow-200',
    success: 'bg-green-50 border-green-200'
  };
  
  return `${baseClasses} ${variantClasses[props.variant]}`;
});

const bodyClasses = computed(() => {
  return 'px-6 py-4';
});

const footerClasses = computed(() => {
  return 'px-6 py-4 border-t border-gray-200 bg-gray-50';
});

const iconClasses = computed(() => {
  const baseClasses = 'w-8 h-8 rounded-full flex items-center justify-center';
  
  const iconColors = {
    info: 'bg-blue-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
    success: 'bg-green-500',
    question: 'bg-purple-500'
  };
  
  return `${baseClasses} ${props.icon ? iconColors[props.icon] : ''}`;
});

const iconComponent = computed(() => {
  if (!props.icon) return null;
  
  const icons = {
    info: {
      template: `
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `
    },
    warning: {
      template: `
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      `
    },
    error: {
      template: `
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `
    },
    success: {
      template: `
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
      `
    },
    question: {
      template: `
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `
    }
  };
  
  return icons[props.icon];
});

// Methods
const close = () => {
  if (!props.persistent) {
    emit('update:modelValue', false);
    emit('close');
  }
};

const cancel = () => {
  emit('cancel');
  close();
};

const confirm = () => {
  emit('confirm');
};

const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    close();
  }
};

const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.closeOnEscape && props.modelValue) {
    close();
  }
};

// Lifecycle
onMounted(() => {
  if (props.closeOnEscape) {
    document.addEventListener('keydown', handleEscapeKey);
  }
});

onUnmounted(() => {
  if (props.closeOnEscape) {
    document.removeEventListener('keydown', handleEscapeKey);
  }
});
</script>

<style scoped>
/* Modal specific styles */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden;
}
</style>

import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

// Import admin components
import { AdminLayout } from '@/components/admin';
import AdminDashboard from '@/pages/admin/AdminDashboard.vue';
import AdminTest from '@/pages/admin/AdminTest.vue';

// Define admin routes
const routes: RouteRecordRaw[] = [
  {
    path: '/admin-spa',
    component: AdminLayout,
    meta: {
      requiresAuth: true,
      requiresAdmin: true,
      title: 'Admin Panel'
    },
    children: [
      {
        path: '',
        redirect: '/admin-spa/dashboard'
      },
      {
        path: 'dashboard',
        name: 'admin-dashboard',
        component: AdminDashboard,
        meta: {
          title: 'Dashboard Overview',
          subtitle: 'Monitor your auction platform performance'
        }
      },
      {
        path: 'test',
        name: 'admin-test',
        component: AdminTest,
        meta: {
          title: 'Component Test',
          subtitle: 'Testing admin layout components'
        }
      },

      // Sales Routes
      {
        path: 'sales',
        name: 'admin-sales',
        redirect: '/admin-spa/sales/overview',
        meta: {
          title: 'Sales Management',
          subtitle: 'Manage sales, orders, and customer relationships'
        }
      },
      {
        path: 'sales/overview',
        name: 'admin-sales-overview',
        component: () => import('@/pages/admin/sales/SalesOverview.vue'),
        meta: {
          title: 'Sales Overview',
          subtitle: 'Monitor sales performance and metrics'
        }
      },
      {
        path: 'sales/orders',
        name: 'admin-sales-orders',
        component: () => import('@/pages/admin/sales/OrdersList.vue'),
        meta: {
          title: 'Orders',
          subtitle: 'Manage customer orders and fulfillment'
        }
      },
      {
        path: 'sales/invoices',
        name: 'admin-sales-invoices',
        component: () => import('@/pages/admin/sales/InvoicesList.vue'),
        meta: {
          title: 'Invoices',
          subtitle: 'Manage sales invoices and billing'
        }
      },
      {
        path: 'sales/customers',
        name: 'admin-sales-customers',
        component: () => import('@/pages/admin/sales/CustomersList.vue'),
        meta: {
          title: 'Customers',
          subtitle: 'Manage customer information and relationships'
        }
      },
      {
        path: 'sales/reports',
        name: 'admin-sales-reports',
        component: () => import('@/pages/admin/sales/SalesReports.vue'),
        meta: {
          title: 'Sales Reports',
          subtitle: 'Analyze sales performance and trends'
        }
      },

      // Auctions Routes
      {
        path: 'auctions',
        name: 'admin-auctions',
        redirect: '/admin-spa/auctions/list',
        meta: {
          title: 'Auctions Management',
          subtitle: 'Manage auction listings and settings'
        }
      },
      {
        path: 'auctions/list',
        name: 'admin-auctions-list',
        component: () => import('@/pages/admin/auctions/AuctionsList.vue'),
        meta: {
          title: 'All Auctions',
          subtitle: 'View and manage all auction listings'
        }
      },
      {
        path: 'auctions/create',
        name: 'admin-auctions-create',
        component: () => import('@/pages/admin/auctions/AuctionForm.vue'),
        meta: {
          title: 'Create Auction',
          subtitle: 'Create a new auction listing'
        }
      },
      {
        path: 'auctions/edit/:id',
        name: 'admin-auctions-edit',
        component: () => import('@/pages/admin/auctions/AuctionForm.vue'),
        meta: {
          title: 'Edit Auction',
          subtitle: 'Modify auction details'
        }
      },
      {
        path: 'auctions/view/:id',
        name: 'admin-auctions-view',
        component: () => import('@/pages/admin/auctions/AuctionDetail.vue'),
        meta: {
          title: 'Auction Details',
          subtitle: 'View auction information and bids'
        }
      },
      {
        path: 'auctions/live',
        name: 'admin-auctions-live',
        component: () => import('@/pages/admin/auctions/LiveAuctions.vue'),
        meta: {
          title: 'Live Auctions',
          subtitle: 'Monitor currently active auctions'
        }
      },
      {
        path: 'auctions/ended',
        name: 'admin-auctions-ended',
        component: () => import('@/pages/admin/auctions/EndedAuctions.vue'),
        meta: {
          title: 'Ended Auctions',
          subtitle: 'View completed auction results'
        }
      },
      {
        path: 'auctions/templates',
        name: 'admin-auctions-templates',
        component: () => import('@/pages/admin/auctions/AuctionTemplates.vue'),
        meta: {
          title: 'Auction Templates',
          subtitle: 'Manage auction templates'
        }
      },

      // Items Routes
      {
        path: 'items',
        name: 'admin-items',
        redirect: '/admin-spa/items/list',
        meta: {
          title: 'Items Management',
          subtitle: 'Manage auction items and inventory'
        }
      },
      {
        path: 'items/list',
        name: 'admin-items-list',
        component: () => import('@/pages/admin/items/ItemsList.vue'),
        meta: {
          title: 'All Items',
          subtitle: 'View and manage all auction items'
        }
      },
      {
        path: 'items/create',
        name: 'admin-items-create',
        component: () => import('@/pages/admin/items/ItemForm.vue'),
        meta: {
          title: 'Add Item',
          subtitle: 'Add a new item to inventory'
        }
      },
      {
        path: 'items/edit/:id',
        name: 'admin-items-edit',
        component: () => import('@/pages/admin/items/ItemForm.vue'),
        meta: {
          title: 'Edit Item',
          subtitle: 'Modify item details'
        }
      },
      {
        path: 'items/categories',
        name: 'admin-items-categories',
        component: () => import('@/pages/admin/items/ItemCategories.vue'),
        meta: {
          title: 'Item Categories',
          subtitle: 'Manage item categories and classifications'
        }
      },
      {
        path: 'items/bulk-import',
        name: 'admin-items-bulk-import',
        component: () => import('@/pages/admin/items/BulkImport.vue'),
        meta: {
          title: 'Bulk Import',
          subtitle: 'Import multiple items from CSV/Excel'
        }
      },
      {
        path: 'items/conditions',
        name: 'admin-items-conditions',
        component: () => import('@/pages/admin/items/ItemConditions.vue'),
        meta: {
          title: 'Item Conditions',
          subtitle: 'Manage item condition standards'
        }
      },

      // Users Routes
      {
        path: 'users',
        name: 'admin-users',
        redirect: '/admin-spa/users/list',
        meta: {
          title: 'User Management',
          subtitle: 'Manage users and permissions'
        }
      },
      {
        path: 'users/list',
        name: 'admin-users-list',
        component: () => import('@/pages/admin/users/UsersList.vue'),
        meta: {
          title: 'All Users',
          subtitle: 'View and manage all users'
        }
      },
      {
        path: 'users/create',
        name: 'admin-users-create',
        component: () => import('@/pages/admin/users/UserForm.vue'),
        meta: {
          title: 'Create User',
          subtitle: 'Add a new user account'
        }
      },
      {
        path: 'users/edit/:id',
        name: 'admin-users-edit',
        component: () => import('@/pages/admin/users/UserForm.vue'),
        meta: {
          title: 'Edit User',
          subtitle: 'Modify user account details'
        }
      },
      {
        path: 'users/bidders',
        name: 'admin-users-bidders',
        component: () => import('@/pages/admin/users/BiddersList.vue'),
        meta: {
          title: 'Bidders',
          subtitle: 'Manage bidder accounts'
        }
      },
      {
        path: 'users/sellers',
        name: 'admin-users-sellers',
        component: () => import('@/pages/admin/users/SellersList.vue'),
        meta: {
          title: 'Sellers',
          subtitle: 'Manage seller accounts'
        }
      },
      {
        path: 'users/administrators',
        name: 'admin-users-administrators',
        component: () => import('@/pages/admin/users/AdministratorsList.vue'),
        meta: {
          title: 'Administrators',
          subtitle: 'Manage admin accounts'
        }
      },
      {
        path: 'users/roles',
        name: 'admin-users-roles',
        component: () => import('@/pages/admin/users/UserRoles.vue'),
        meta: {
          title: 'User Roles',
          subtitle: 'Manage user roles and permissions'
        }
      },
      {
        path: 'users/permissions',
        name: 'admin-users-permissions',
        component: () => import('@/pages/admin/users/UserPermissions.vue'),
        meta: {
          title: 'Permissions',
          subtitle: 'Configure user permissions'
        }
      },

      // Financial Routes
      {
        path: 'financial',
        name: 'admin-financial',
        redirect: '/admin-spa/financial/transactions',
        meta: {
          title: 'Financial Management',
          subtitle: 'Manage payments and financial reports'
        }
      },
      {
        path: 'financial/transactions',
        name: 'admin-financial-transactions',
        component: () => import('@/pages/admin/financial/TransactionsList.vue'),
        meta: {
          title: 'Transactions',
          subtitle: 'View all financial transactions'
        }
      },
      {
        path: 'financial/payments',
        name: 'admin-financial-payments',
        component: () => import('@/pages/admin/financial/PaymentsList.vue'),
        meta: {
          title: 'Payments',
          subtitle: 'Manage payment processing'
        }
      },
      {
        path: 'financial/commissions',
        name: 'admin-financial-commissions',
        component: () => import('@/pages/admin/financial/CommissionsList.vue'),
        meta: {
          title: 'Commissions',
          subtitle: 'Track commission payments'
        }
      },
      {
        path: 'financial/invoices',
        name: 'admin-financial-invoices',
        component: () => import('@/pages/admin/financial/InvoicesList.vue'),
        meta: {
          title: 'Invoices',
          subtitle: 'Manage invoices and billing'
        }
      },
      {
        path: 'financial/tax-reports',
        name: 'admin-financial-tax-reports',
        component: () => import('@/pages/admin/financial/TaxReports.vue'),
        meta: {
          title: 'Tax Reports',
          subtitle: 'Generate tax reporting documents'
        }
      },

      // Reports Routes
      {
        path: 'reports',
        name: 'admin-reports',
        redirect: '/admin-spa/reports/sales',
        meta: {
          title: 'Reports & Analytics',
          subtitle: 'View performance reports and analytics'
        }
      },
      {
        path: 'reports/sales',
        name: 'admin-reports-sales',
        component: () => import('@/pages/admin/reports/SalesReports.vue'),
        meta: {
          title: 'Sales Reports',
          subtitle: 'Analyze sales performance and trends'
        }
      },
      {
        path: 'reports/analytics',
        name: 'admin-reports-analytics',
        component: () => import('@/pages/admin/reports/UserAnalytics.vue'),
        meta: {
          title: 'User Analytics',
          subtitle: 'Track user behavior and engagement'
        }
      },
      {
        path: 'reports/performance',
        name: 'admin-reports-performance',
        component: () => import('@/pages/admin/reports/PerformanceReports.vue'),
        meta: {
          title: 'Performance Reports',
          subtitle: 'Monitor system and auction performance'
        }
      },
      {
        path: 'reports/custom',
        name: 'admin-reports-custom',
        component: () => import('@/pages/admin/reports/CustomReports.vue'),
        meta: {
          title: 'Custom Reports',
          subtitle: 'Create and manage custom reports'
        }
      },

      // Settings Routes
      {
        path: 'settings',
        name: 'admin-settings',
        redirect: '/admin-spa/settings/general',
        meta: {
          title: 'System Settings',
          subtitle: 'Configure system preferences and options'
        }
      },
      {
        path: 'settings/general',
        name: 'admin-settings-general',
        component: () => import('@/pages/admin/settings/GeneralSettings.vue'),
        meta: {
          title: 'General Settings',
          subtitle: 'Configure general system settings'
        }
      },
      {
        path: 'settings/auctions',
        name: 'admin-settings-auctions',
        component: () => import('@/pages/admin/settings/AuctionSettings.vue'),
        meta: {
          title: 'Auction Settings',
          subtitle: 'Configure auction parameters and rules'
        }
      },
      {
        path: 'settings/payments',
        name: 'admin-settings-payments',
        component: () => import('@/pages/admin/settings/PaymentSettings.vue'),
        meta: {
          title: 'Payment Settings',
          subtitle: 'Configure payment gateways and options'
        }
      },
      {
        path: 'settings/email',
        name: 'admin-settings-email',
        component: () => import('@/pages/admin/settings/EmailTemplates.vue'),
        meta: {
          title: 'Email Templates',
          subtitle: 'Manage email templates and notifications'
        }
      },
      {
        path: 'settings/logs',
        name: 'admin-settings-logs',
        component: () => import('@/pages/admin/settings/SystemLogs.vue'),
        meta: {
          title: 'System Logs',
          subtitle: 'View system logs and error reports'
        }
      }
    ]
  }
];

// Create router instance
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// Navigation guards
router.beforeEach(async (to, _from, next) => {
  // Set page title
  if (to.meta.title) {
    document.title = `${to.meta.title} - Vertigo AMS Admin`;
  }

  // Initialize auth store if needed
  const authStore = useAuthStore();

  // Always try to initialize auth store to check session
  if (!authStore.user) {
    try {
      await authStore.initialize();
    } catch (error) {
      console.error('Failed to initialize auth:', error);
    }
  }

  // Check authentication requirements
  if (to.meta.requiresAuth) {
    const isAuthenticated = checkAuthentication();

    if (!isAuthenticated) {
      // Redirect to login with return URL
      const returnUrl = encodeURIComponent(to.fullPath);
      window.location.href = `/login?redirect=${returnUrl}`;
      return;
    }
  }

  // Check admin requirements
  if (to.meta.requiresAdmin) {
    const isAdmin = checkAdminAccess();

    if (!isAdmin) {
      // Redirect to unauthorized page or home
      window.location.href = '/';
      return;
    }
  }

  next();
});

// Helper functions for authentication checks
function checkAuthentication(): boolean {
  const authStore = useAuthStore();

  // Check if user is authenticated via session (Laravel auth)
  const isSessionAuth = authStore.sessionAuth || (authStore.user && !authStore.token);
  const isTokenAuth = !!(authStore.token && authStore.user);

  return !!(isSessionAuth || isTokenAuth || authStore.isAuthenticated);
}

function checkAdminAccess(): boolean {
  const authStore = useAuthStore();

  // Check if user has admin role or permissions
  const user = authStore.user as any;

  if (!user) return false;

  // Check for admin role
  const hasAdminRole = user.roles?.some((role: any) =>
    role.name === 'admin' ||
    role.name === 'administrator' ||
    role.name === 'super-admin' ||
    role.name === 'staff' ||
    role.name === 'manager'
  );

  // Check for admin permissions
  const hasAdminPermission = user.permissions?.some((permission: any) =>
    permission.name === 'admin-access' ||
    permission.name === 'admin-panel' ||
    permission.name === 'manage-auctions' ||
    permission.name === 'manage-users'
  );

  // Check if user is not a customer or supplier (based on existing middleware logic)
  const isNotCustomer = !user.isCustomer?.() && !user.isSupplier?.();

  return !!(hasAdminRole || hasAdminPermission || isNotCustomer);
}

export default router;

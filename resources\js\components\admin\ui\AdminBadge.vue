<template>
  <span :class="badgeClasses">
    <!-- Icon -->
    <component
      v-if="icon"
      :is="iconComponent"
      :class="iconClasses"
    />
    
    <!-- Content -->
    <span v-if="$slots.default">
      <slot />
    </span>
    <span v-else-if="text">
      {{ text }}
    </span>
    
    <!-- Dot indicator -->
    <span v-if="dot" :class="dotClasses" />
    
    <!-- Close button -->
    <button
      v-if="closable"
      @click="handleClose"
      :class="closeButtonClasses"
      type="button"
    >
      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  text?: string;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'dark';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  rounded?: boolean;
  outlined?: boolean;
  dot?: boolean;
  icon?: string;
  closable?: boolean;
  pulse?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'sm',
  rounded: false,
  outlined: false,
  dot: false,
  closable: false,
  pulse: false
});

const emit = defineEmits<{
  close: [];
}>();

// Icon components
const iconComponents = {
  check: {
    template: `
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
      </svg>
    `
  },
  x: {
    template: `
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    `
  },
  exclamation: {
    template: `
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    `
  },
  info: {
    template: `
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    `
  },
  star: {
    template: `
      <svg fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
      </svg>
    `
  },
  clock: {
    template: `
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    `
  },
  user: {
    template: `
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
    `
  }
};

// Computed
const badgeClasses = computed(() => {
  const baseClasses = 'inline-flex items-center font-medium transition-all duration-200';
  
  // Size classes
  const sizeClasses = {
    xs: 'px-2 py-0.5 text-xs',
    sm: 'px-2.5 py-0.5 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-1 text-sm'
  };
  
  // Variant classes
  const variantClasses = props.outlined ? {
    default: 'text-gray-700 bg-white border border-gray-300',
    primary: 'text-blue-700 bg-blue-50 border border-blue-200',
    secondary: 'text-gray-700 bg-gray-50 border border-gray-200',
    success: 'text-green-700 bg-green-50 border border-green-200',
    danger: 'text-red-700 bg-red-50 border border-red-200',
    warning: 'text-yellow-700 bg-yellow-50 border border-yellow-200',
    info: 'text-blue-700 bg-blue-50 border border-blue-200',
    dark: 'text-gray-700 bg-gray-50 border border-gray-200'
  } : {
    default: 'text-gray-800 bg-gray-100',
    primary: 'text-blue-800 bg-blue-100',
    secondary: 'text-gray-800 bg-gray-200',
    success: 'text-green-800 bg-green-100',
    danger: 'text-red-800 bg-red-100',
    warning: 'text-yellow-800 bg-yellow-100',
    info: 'text-blue-800 bg-blue-100',
    dark: 'text-white bg-gray-800'
  };
  
  // Border radius
  const roundedClasses = props.rounded ? 'rounded-full' : 'rounded-md';
  
  // Pulse animation
  const pulseClasses = props.pulse ? 'animate-pulse' : '';
  
  return [
    baseClasses,
    sizeClasses[props.size],
    variantClasses[props.variant],
    roundedClasses,
    pulseClasses
  ].filter(Boolean).join(' ');
});

const iconComponent = computed(() => {
  return props.icon && iconComponents[props.icon as keyof typeof iconComponents]
    ? iconComponents[props.icon as keyof typeof iconComponents]
    : null;
});

const iconClasses = computed(() => {
  const baseClasses = 'w-3 h-3';
  const spacingClasses = props.text || props.$slots.default ? 'mr-1' : '';
  return `${baseClasses} ${spacingClasses}`;
});

const dotClasses = computed(() => {
  const baseClasses = 'w-2 h-2 rounded-full';
  
  const dotColors = {
    default: 'bg-gray-400',
    primary: 'bg-blue-400',
    secondary: 'bg-gray-400',
    success: 'bg-green-400',
    danger: 'bg-red-400',
    warning: 'bg-yellow-400',
    info: 'bg-blue-400',
    dark: 'bg-gray-600'
  };
  
  const spacingClasses = (props.text || props.$slots.default || props.icon) ? 'ml-1' : '';
  const pulseClasses = props.pulse ? 'animate-pulse' : '';
  
  return [
    baseClasses,
    dotColors[props.variant],
    spacingClasses,
    pulseClasses
  ].filter(Boolean).join(' ');
});

const closeButtonClasses = computed(() => {
  const baseClasses = 'ml-1 inline-flex items-center justify-center rounded-full hover:bg-black hover:bg-opacity-10 focus:outline-none focus:bg-black focus:bg-opacity-10 transition-colors duration-200';
  const sizeClasses = props.size === 'xs' ? 'w-4 h-4' : 'w-5 h-5';
  
  return `${baseClasses} ${sizeClasses}`;
});

// Methods
const handleClose = () => {
  emit('close');
};
</script>

<style scoped>
/* Admin badge specific styles */
.admin-badge {
  @apply transition-all duration-200 ease-in-out;
}

/* Pulse animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Hover effects */
.admin-badge:hover {
  @apply transform scale-105;
}

/* Focus styles for closable badges */
.admin-badge button:focus {
  @apply outline-none ring-2 ring-offset-1 ring-blue-500;
}
</style>

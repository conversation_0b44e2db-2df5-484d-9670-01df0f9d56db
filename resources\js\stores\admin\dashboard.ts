import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// Dashboard Stats Interface
export interface DashboardStats {
  activeAuctions: number;
  activeAuctionsChange: number;
  totalRevenue: number;
  revenueChange: number;
  totalItems: number;
  itemsChange: number;
  totalUsers: number;
  usersChange: number;
  totalBids: number;
  bidsChange: number;
  conversionRate: number;
  conversionRateChange: number;
}

// Recent Activity Interface
export interface RecentActivity {
  id: string;
  type: 'auction_created' | 'auction_ended' | 'bid_placed' | 'user_registered' | 'payment_received';
  title: string;
  description: string;
  timestamp: string;
  user?: {
    id: number;
    name: string;
    avatar?: string;
  };
  metadata?: Record<string, any>;
}

// System Status Interface
export interface SystemStatus {
  service: string;
  status: 'online' | 'warning' | 'error' | 'maintenance';
  lastChecked: string;
  responseTime?: number;
  uptime?: number;
}

// Chart Data Interface
export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    borderWidth?: number;
    fill?: boolean;
  }>;
}

// Widget Configuration Interface
export interface DashboardWidget {
  id: string;
  title: string;
  type: 'stats' | 'chart' | 'table' | 'activity' | 'status';
  position: { x: number; y: number; w: number; h: number };
  visible: boolean;
  config?: Record<string, any>;
}

export const useAdminDashboard = defineStore('adminDashboard', () => {
  // State
  const stats = ref<DashboardStats>({
    activeAuctions: 0,
    activeAuctionsChange: 0,
    totalRevenue: 0,
    revenueChange: 0,
    totalItems: 0,
    itemsChange: 0,
    totalUsers: 0,
    usersChange: 0,
    totalBids: 0,
    bidsChange: 0,
    conversionRate: 0,
    conversionRateChange: 0
  });

  const recentActivity = ref<RecentActivity[]>([]);
  const systemStatus = ref<SystemStatus[]>([]);
  const revenueChartData = ref<ChartData | null>(null);
  const auctionChartData = ref<ChartData | null>(null);
  const userChartData = ref<ChartData | null>(null);
  
  const widgets = ref<DashboardWidget[]>([
    {
      id: 'stats-overview',
      title: 'Overview Stats',
      type: 'stats',
      position: { x: 0, y: 0, w: 12, h: 2 },
      visible: true
    },
    {
      id: 'revenue-chart',
      title: 'Revenue Trends',
      type: 'chart',
      position: { x: 0, y: 2, w: 8, h: 4 },
      visible: true,
      config: { chartType: 'line', timeRange: '30d' }
    },
    {
      id: 'recent-activity',
      title: 'Recent Activity',
      type: 'activity',
      position: { x: 8, y: 2, w: 4, h: 4 },
      visible: true
    },
    {
      id: 'system-status',
      title: 'System Status',
      type: 'status',
      position: { x: 0, y: 6, w: 6, h: 3 },
      visible: true
    }
  ]);

  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const lastRefresh = ref<Date | null>(null);
  const autoRefresh = ref(true);
  const refreshInterval = ref(30000); // 30 seconds

  // Getters
  const totalRevenueCurrency = computed(() => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(stats.value.totalRevenue);
  });

  const hasRecentActivity = computed(() => recentActivity.value.length > 0);
  const systemHealthy = computed(() => 
    systemStatus.value.every(status => status.status === 'online')
  );
  
  const visibleWidgets = computed(() => 
    widgets.value.filter(widget => widget.visible)
  );

  // Actions
  const fetchDashboardData = async (force = false) => {
    if (isLoading.value && !force) return;
    
    isLoading.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/admin/dashboard', {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }

      const data = await response.json();
      
      // Update stats
      if (data.stats) {
        stats.value = { ...stats.value, ...data.stats };
      }

      // Update recent activity
      if (data.recentActivity) {
        recentActivity.value = data.recentActivity;
      }

      // Update system status
      if (data.systemStatus) {
        systemStatus.value = data.systemStatus;
      }

      // Update chart data
      if (data.charts) {
        if (data.charts.revenue) {
          revenueChartData.value = data.charts.revenue;
        }
        if (data.charts.auctions) {
          auctionChartData.value = data.charts.auctions;
        }
        if (data.charts.users) {
          userChartData.value = data.charts.users;
        }
      }

      lastRefresh.value = new Date();
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      console.error('Dashboard fetch error:', err);
    } finally {
      isLoading.value = false;
    }
  };

  const refreshStats = async () => {
    await fetchDashboardData(true);
  };

  const updateWidget = (widgetId: string, updates: Partial<DashboardWidget>) => {
    const index = widgets.value.findIndex(w => w.id === widgetId);
    if (index !== -1) {
      widgets.value[index] = { ...widgets.value[index], ...updates };
      saveWidgetLayout();
    }
  };

  const toggleWidget = (widgetId: string) => {
    const widget = widgets.value.find(w => w.id === widgetId);
    if (widget) {
      widget.visible = !widget.visible;
      saveWidgetLayout();
    }
  };

  const saveWidgetLayout = () => {
    localStorage.setItem('admin-dashboard-widgets', JSON.stringify(widgets.value));
  };

  const loadWidgetLayout = () => {
    const saved = localStorage.getItem('admin-dashboard-widgets');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        widgets.value = parsed;
      } catch (error) {
        console.error('Failed to parse widget layout:', error);
      }
    }
  };

  const resetWidgetLayout = () => {
    // Reset to default layout
    widgets.value = [
      {
        id: 'stats-overview',
        title: 'Overview Stats',
        type: 'stats',
        position: { x: 0, y: 0, w: 12, h: 2 },
        visible: true
      },
      {
        id: 'revenue-chart',
        title: 'Revenue Trends',
        type: 'chart',
        position: { x: 0, y: 2, w: 8, h: 4 },
        visible: true,
        config: { chartType: 'line', timeRange: '30d' }
      },
      {
        id: 'recent-activity',
        title: 'Recent Activity',
        type: 'activity',
        position: { x: 8, y: 2, w: 4, h: 4 },
        visible: true
      },
      {
        id: 'system-status',
        title: 'System Status',
        type: 'status',
        position: { x: 0, y: 6, w: 6, h: 3 },
        visible: true
      }
    ];
    saveWidgetLayout();
  };

  const setAutoRefresh = (enabled: boolean, interval?: number) => {
    autoRefresh.value = enabled;
    if (interval) {
      refreshInterval.value = interval;
    }
    localStorage.setItem('admin-dashboard-auto-refresh', JSON.stringify({
      enabled: autoRefresh.value,
      interval: refreshInterval.value
    }));
  };

  const initialize = async () => {
    // Load widget layout
    loadWidgetLayout();
    
    // Load auto-refresh settings
    const savedRefreshSettings = localStorage.getItem('admin-dashboard-auto-refresh');
    if (savedRefreshSettings) {
      try {
        const parsed = JSON.parse(savedRefreshSettings);
        autoRefresh.value = parsed.enabled;
        refreshInterval.value = parsed.interval;
      } catch (error) {
        console.error('Failed to parse auto-refresh settings:', error);
      }
    }

    // Fetch initial data
    await fetchDashboardData();
  };

  const reset = () => {
    stats.value = {
      activeAuctions: 0,
      activeAuctionsChange: 0,
      totalRevenue: 0,
      revenueChange: 0,
      totalItems: 0,
      itemsChange: 0,
      totalUsers: 0,
      usersChange: 0,
      totalBids: 0,
      bidsChange: 0,
      conversionRate: 0,
      conversionRateChange: 0
    };
    recentActivity.value = [];
    systemStatus.value = [];
    revenueChartData.value = null;
    auctionChartData.value = null;
    userChartData.value = null;
    error.value = null;
    lastRefresh.value = null;
    
    // Clear localStorage
    localStorage.removeItem('admin-dashboard-widgets');
    localStorage.removeItem('admin-dashboard-auto-refresh');
  };

  return {
    // State
    stats,
    recentActivity,
    systemStatus,
    revenueChartData,
    auctionChartData,
    userChartData,
    widgets,
    isLoading,
    error,
    lastRefresh,
    autoRefresh,
    refreshInterval,

    // Getters
    totalRevenueCurrency,
    hasRecentActivity,
    systemHealthy,
    visibleWidgets,

    // Actions
    fetchDashboardData,
    refreshStats,
    updateWidget,
    toggleWidget,
    saveWidgetLayout,
    loadWidgetLayout,
    resetWidgetLayout,
    setAutoRefresh,
    initialize,
    reset
  };
});

export type AdminDashboardStore = ReturnType<typeof useAdminDashboard>;

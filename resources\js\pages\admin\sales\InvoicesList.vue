<template>
  <AdminListTemplate
    title="Invoices"
    subtitle="Manage customer invoices and billing"
    :loading="loading"
    :error="error"
    :items="invoices"
    :columns="columns"
    :show-create-button="true"
    create-button-text="New Invoice"
    create-route="/admin-spa/sales/invoices/create"
    empty-state-title="No invoices found"
    empty-state-message="Invoices will appear here when orders are processed."
    :show-pagination="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
  >
    <template #actions>
      <div class="flex space-x-3">
        <Button variant="outline" size="sm" @click="exportInvoices">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"/>
          </svg>
          Export
        </Button>
        <Button variant="primary" size="sm" @click="handleCreate">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          New Invoice
        </Button>
      </div>
    </template>

    <template #filters>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Select
          v-model="filters.status"
          placeholder="Filter by status"
          :options="statusOptions"
          @change="applyFilters"
        />
        <Select
          v-model="filters.dateRange"
          placeholder="Date range"
          :options="dateRangeOptions"
          @change="applyFilters"
        />
        <Input
          v-model="filters.customer"
          placeholder="Search customer..."
          @input="applyFilters"
        />
        <Input
          v-model="filters.invoiceNumber"
          placeholder="Invoice number..."
          @input="applyFilters"
        />
      </div>
    </template>

    <template #item-actions="{ item }">
      <div class="flex space-x-2">
        <Button variant="ghost" size="sm" @click="viewInvoice(item)">
          View
        </Button>
        <Button variant="ghost" size="sm" @click="downloadPDF(item)">
          PDF
        </Button>
        <Button variant="ghost" size="sm" @click="sendInvoice(item)">
          Send
        </Button>
        <Button 
          v-if="item.status === 'draft'" 
          variant="ghost" 
          size="sm" 
          @click="editInvoice(item)"
        >
          Edit
        </Button>
      </div>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Select, Input } from '@/components/ui';

// Router
const router = useRouter();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const currentPage = ref(1);
const totalPages = ref(3);
const totalItems = ref(45);

// Filters
const filters = ref({
  status: '',
  dateRange: '',
  customer: '',
  invoiceNumber: ''
});

// Options
const statusOptions = [
  { value: '', label: 'All Statuses' },
  { value: 'draft', label: 'Draft' },
  { value: 'sent', label: 'Sent' },
  { value: 'paid', label: 'Paid' },
  { value: 'overdue', label: 'Overdue' },
  { value: 'cancelled', label: 'Cancelled' }
];

const dateRangeOptions = [
  { value: '', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' }
];

// Table columns
const columns = [
  { key: 'number', label: 'Invoice #', sortable: true },
  { key: 'customer', label: 'Customer', sortable: true },
  { key: 'amount', label: 'Amount', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'dueDate', label: 'Due Date', sortable: true },
  { key: 'date', label: 'Created', sortable: true },
  { key: 'actions', label: 'Actions', sortable: false }
];

// Mock data
const allInvoices = ref([
  {
    id: 1,
    number: 'INV-2024-001',
    customer: 'John Doe',
    customerEmail: '<EMAIL>',
    amount: 299.99,
    status: 'paid',
    date: new Date('2024-01-15'),
    dueDate: new Date('2024-02-15'),
    orderId: 1001
  },
  {
    id: 2,
    number: 'INV-2024-002',
    customer: 'Jane Smith',
    customerEmail: '<EMAIL>',
    amount: 149.50,
    status: 'sent',
    date: new Date('2024-01-14'),
    dueDate: new Date('2024-02-14'),
    orderId: 1002
  },
  {
    id: 3,
    number: 'INV-2024-003',
    customer: 'Bob Johnson',
    customerEmail: '<EMAIL>',
    amount: 399.99,
    status: 'overdue',
    date: new Date('2024-01-10'),
    dueDate: new Date('2024-02-10'),
    orderId: 1003
  },
  {
    id: 4,
    number: 'INV-2024-004',
    customer: 'Alice Brown',
    customerEmail: '<EMAIL>',
    amount: 199.99,
    status: 'draft',
    date: new Date('2024-01-12'),
    dueDate: new Date('2024-02-12'),
    orderId: 1004
  }
]);

// Computed
const invoices = computed(() => {
  let filtered = allInvoices.value;

  // Apply filters
  if (filters.value.status) {
    filtered = filtered.filter(invoice => invoice.status === filters.value.status);
  }
  if (filters.value.customer) {
    filtered = filtered.filter(invoice => 
      invoice.customer.toLowerCase().includes(filters.value.customer.toLowerCase()) ||
      invoice.customerEmail.toLowerCase().includes(filters.value.customer.toLowerCase())
    );
  }
  if (filters.value.invoiceNumber) {
    filtered = filtered.filter(invoice => 
      invoice.number.toLowerCase().includes(filters.value.invoiceNumber.toLowerCase())
    );
  }

  return filtered;
});

// Methods
const handleCreate = () => {
  router.push('/admin-spa/sales/invoices/create');
};

const handleSearch = (query: string) => {
  filters.value.customer = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  console.log('Sorting by:', column, order);
  // Implement sorting logic
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  // Load data for new page
};

const applyFilters = () => {
  currentPage.value = 1;
  // Apply filters and reload data
};

const exportInvoices = () => {
  console.log('Exporting invoices...');
};

const viewInvoice = (invoice: any) => {
  router.push(`/admin-spa/sales/invoices/${invoice.id}`);
};

const editInvoice = (invoice: any) => {
  router.push(`/admin-spa/sales/invoices/${invoice.id}/edit`);
};

const downloadPDF = (invoice: any) => {
  console.log('Downloading PDF for invoice:', invoice.number);
  // Implement PDF download
};

const sendInvoice = (invoice: any) => {
  console.log('Sending invoice:', invoice.number);
  // Implement invoice sending
};

// Lifecycle
onMounted(() => {
  // Load invoices data
});
</script>

<template>
  <AdminFormTemplate
    :title="isEdit ? 'Edit Auction' : 'Create Auction'"
    :subtitle="isEdit ? 'Update auction details and settings' : 'Set up a new auction with items and bidding rules'"
    :loading="loading"
    :error="error"
    :saving="saving"
    :is-valid="isValid"
    :validation-errors="validationErrors"
    :save-button-text="isEdit ? 'Update Auction' : 'Create Auction'"
    :show-draft="true"
    :saving-draft="savingDraft"
    cancel-route="/admin-spa/auctions"
    @save="handleSave"
    @save-draft="handleSaveDraft"
    @cancel="handleCancel"
  >
    <!-- Basic Information Section -->
    <template #section-basic>
      <div class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Auction Title *
            </label>
            <Input
              v-model="form.title"
              placeholder="Enter auction title"
              :error="getFieldError('title')"
              required
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Auction Type *
            </label>
            <Select
              v-model="form.type"
              :options="auctionTypeOptions"
              placeholder="Select auction type"
              :error="getFieldError('type')"
              required
            />
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            v-model="form.description"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Describe the auction..."
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Start Date *
            </label>
            <Input
              v-model="form.startDate"
              type="datetime-local"
              :error="getFieldError('startDate')"
              required
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              End Date *
            </label>
            <Input
              v-model="form.endDate"
              type="datetime-local"
              :error="getFieldError('endDate')"
              required
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <Select
              v-model="form.status"
              :options="statusOptions"
              placeholder="Select status"
            />
          </div>
        </div>
      </div>
    </template>

    <!-- Bidding Rules Section -->
    <template #section-bidding>
      <div class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Starting Bid
            </label>
            <Input
              v-model="form.startingBid"
              type="number"
              step="0.01"
              placeholder="0.00"
              :error="getFieldError('startingBid')"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Bid Increment
            </label>
            <Input
              v-model="form.bidIncrement"
              type="number"
              step="0.01"
              placeholder="1.00"
              :error="getFieldError('bidIncrement')"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Reserve Price
            </label>
            <Input
              v-model="form.reservePrice"
              type="number"
              step="0.01"
              placeholder="0.00"
              :error="getFieldError('reservePrice')"
            />
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="flex items-center">
              <input
                v-model="form.allowProxyBids"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
              <span class="ml-2 text-sm text-gray-700">Allow Proxy Bidding</span>
            </label>
          </div>
          <div>
            <label class="flex items-center">
              <input
                v-model="form.autoExtend"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
              <span class="ml-2 text-sm text-gray-700">Auto-extend on Late Bids</span>
            </label>
          </div>
        </div>

        <div v-if="form.autoExtend" class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Extension Time (minutes)
            </label>
            <Input
              v-model="form.extensionTime"
              type="number"
              placeholder="5"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Extension Trigger (minutes before end)
            </label>
            <Input
              v-model="form.extensionTrigger"
              type="number"
              placeholder="2"
            />
          </div>
        </div>
      </div>
    </template>

    <!-- Settings Section -->
    <template #section-settings>
      <div class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Preview Start Date
            </label>
            <Input
              v-model="form.previewStartDate"
              type="datetime-local"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Registration Deadline
            </label>
            <Input
              v-model="form.registrationDeadline"
              type="datetime-local"
            />
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="flex items-center">
              <input
                v-model="form.requireRegistration"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
              <span class="ml-2 text-sm text-gray-700">Require Registration</span>
            </label>
          </div>
          <div>
            <label class="flex items-center">
              <input
                v-model="form.isPublic"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
              <span class="ml-2 text-sm text-gray-700">Public Auction</span>
            </label>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Terms and Conditions
          </label>
          <textarea
            v-model="form.terms"
            rows="6"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter auction terms and conditions..."
          />
        </div>
      </div>
    </template>
  </AdminFormTemplate>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { AdminFormTemplate } from '@/components/admin';
import { Input, Select } from '@/components/ui';

// Router
const router = useRouter();
const route = useRoute();

// State
const loading = ref(false);
const saving = ref(false);
const savingDraft = ref(false);
const error = ref<string | null>(null);
const validationErrors = ref<string[]>([]);

// Form data
const form = ref({
  title: '',
  description: '',
  type: '',
  status: 'draft',
  startDate: '',
  endDate: '',
  previewStartDate: '',
  registrationDeadline: '',
  startingBid: 0,
  bidIncrement: 1,
  reservePrice: 0,
  allowProxyBids: true,
  autoExtend: false,
  extensionTime: 5,
  extensionTrigger: 2,
  requireRegistration: false,
  isPublic: true,
  terms: ''
});

// Options
const auctionTypeOptions = [
  { value: 'live', label: 'Live Auction' },
  { value: 'timed', label: 'Timed Auction' },
  { value: 'silent', label: 'Silent Auction' },
  { value: 'sealed', label: 'Sealed Bid' }
];

const statusOptions = [
  { value: 'draft', label: 'Draft' },
  { value: 'scheduled', label: 'Scheduled' },
  { value: 'active', label: 'Active' },
  { value: 'paused', label: 'Paused' },
  { value: 'ended', label: 'Ended' },
  { value: 'cancelled', label: 'Cancelled' }
];

// Computed
const isEdit = computed(() => !!route.params.id);

const isValid = computed(() => {
  return form.value.title && 
         form.value.type && 
         form.value.startDate && 
         form.value.endDate;
});

// Methods
const getFieldError = (field: string) => {
  return validationErrors.value.find(error => error.includes(field));
};

const handleSave = async () => {
  saving.value = true;
  validationErrors.value = [];
  
  try {
    // Validate form
    if (!isValid.value) {
      validationErrors.value.push('Please fill in all required fields');
      return;
    }
    
    // Save auction
    console.log('Saving auction:', form.value);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Redirect to auction list
    router.push('/admin-spa/auctions');
  } catch (err) {
    error.value = 'Failed to save auction';
  } finally {
    saving.value = false;
  }
};

const handleSaveDraft = async () => {
  savingDraft.value = true;
  
  try {
    console.log('Saving draft:', form.value);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Show success message
  } catch (err) {
    error.value = 'Failed to save draft';
  } finally {
    savingDraft.value = false;
  }
};

const handleCancel = () => {
  router.push('/admin-spa/auctions');
};

// Lifecycle
onMounted(async () => {
  if (isEdit.value) {
    loading.value = true;
    try {
      // Load auction data
      const auctionId = route.params.id;
      console.log('Loading auction:', auctionId);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Populate form with existing data
      // form.value = { ...existingAuctionData };
    } catch (err) {
      error.value = 'Failed to load auction';
    } finally {
      loading.value = false;
    }
  }
});
</script>

<template>
  <AdminPageTemplate
    title="Dashboard Overview"
    subtitle="Monitor your auction platform performance"
    :loading="loading"
    :error="error"
  >
    <template #actions>
      <div class="flex space-x-3">
        <Button variant="outline" size="sm" @click="exportData">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"/>
          </svg>
          Export
        </Button>
        <Button variant="primary" size="sm" @click="createAuction">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Create Auction
        </Button>
        <Button variant="outline" size="sm" @click="handleRefresh" :loading="refreshing">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </Button>
      </div>
    </template>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <AdminStatsCard
        title="Active Auctions"
        :value="stats.activeAuctions"
        :change="stats.activeAuctionsChange"
        icon="auctions"
        color="blue"
        :loading="loading"
      />
      <AdminStatsCard
        title="Total Revenue"
        :value="dashboardStore.totalRevenueCurrency"
        :change="stats.revenueChange"
        icon="revenue"
        color="green"
        :loading="loading"
      />
      <AdminStatsCard
        title="Total Items"
        :value="stats.totalItems"
        :change="stats.itemsChange"
        icon="items"
        color="yellow"
        :loading="loading"
      />
      <AdminStatsCard
        title="Registered Users"
        :value="stats.totalUsers"
        :change="stats.usersChange"
        icon="users"
        color="purple"
        :loading="loading"
      />
    </div>

    <!-- Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Auctions -->
      <Card class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Recent Auctions</h3>
          <router-link
            to="/admin-spa/auctions/list"
            class="text-sm text-blue-600 hover:text-blue-800"
          >
            View all
          </router-link>
        </div>
        <div class="space-y-3">
          <div
            v-for="auction in recentAuctions"
            :key="auction.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div>
              <p class="font-medium text-gray-900">{{ auction.title }}</p>
              <p class="text-sm text-gray-500">{{ auction.status }}</p>
            </div>
            <span class="text-green-600 font-semibold">{{ formatCurrency(auction.currentBid) }}</span>
          </div>
        </div>
        <div v-if="recentAuctions.length === 0" class="text-center py-8 text-gray-500">
          No recent auctions
        </div>
      </Card>

      <!-- System Status -->
      <Card class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
        <div class="space-y-3">
          <div
            v-for="status in systemStatus"
            :key="status.service"
            class="flex items-center justify-between"
          >
            <span class="text-gray-600">{{ status.service }}</span>
            <Badge :variant="status.variant">{{ status.status }}</Badge>
          </div>
        </div>
      </Card>
    </div>

    <!-- Quick Actions -->
    <Card class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Button
          variant="outline"
          class="h-20 flex flex-col items-center justify-center space-y-2"
          @click="$router.push('/admin-spa/auctions/create')"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          <span class="text-sm">Create Auction</span>
        </Button>
        <Button
          variant="outline"
          class="h-20 flex flex-col items-center justify-center space-y-2"
          @click="$router.push('/admin-spa/items/create')"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
          </svg>
          <span class="text-sm">Add Item</span>
        </Button>
        <Button
          variant="outline"
          class="h-20 flex flex-col items-center justify-center space-y-2"
          @click="$router.push('/admin-spa/users/create')"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
          </svg>
          <span class="text-sm">Add User</span>
        </Button>
        <Button
          variant="outline"
          class="h-20 flex flex-col items-center justify-center space-y-2"
          @click="$router.push('/admin-spa/reports/sales')"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          <span class="text-sm">View Reports</span>
        </Button>
      </div>
    </Card>
  </AdminPageTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { AdminPageTemplate, AdminStatsCard, useAdminDashboard } from '@/components/admin';
import { Button, Card, Badge } from '@/components/ui';

// Router
const router = useRouter();

// Store
const dashboardStore = useAdminDashboard();

// State
const refreshing = ref(false);

// Computed properties from store
const loading = computed(() => dashboardStore.isLoading);
const error = computed(() => dashboardStore.error);
const stats = computed(() => dashboardStore.stats);
const recentActivity = computed(() => dashboardStore.recentActivity);
const systemStatus = computed(() => dashboardStore.systemStatus);
const lastRefresh = computed(() => dashboardStore.lastRefresh);

// Mock data for recent auctions (until we have real data)
const recentAuctions = ref([
  {
    id: 1,
    title: 'Vintage Watch Collection',
    status: 'Ends in 2 hours',
    currentBid: 1250
  },
  {
    id: 2,
    title: 'Art Deco Furniture',
    status: 'Ends in 1 day',
    currentBid: 850
  },
  {
    id: 3,
    title: 'Classic Car Parts',
    status: 'Ends in 3 days',
    currentBid: 2100
  }
]);

// Auto-refresh interval
let refreshInterval: number | null = null;

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const handleRefresh = async () => {
  refreshing.value = true;
  try {
    await dashboardStore.refreshStats();
  } catch (err) {
    console.error('Failed to refresh dashboard:', err);
  } finally {
    refreshing.value = false;
  }
};

const setupAutoRefresh = () => {
  if (dashboardStore.autoRefresh && !refreshInterval) {
    refreshInterval = window.setInterval(() => {
      dashboardStore.fetchDashboardData();
    }, dashboardStore.refreshInterval);
  }
};

const clearAutoRefresh = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
  }
};

const exportData = () => {
  // Implement export functionality
  console.log('Exporting dashboard data...');
};

// Lifecycle hooks
onMounted(async () => {
  try {
    await dashboardStore.initialize();
    setupAutoRefresh();
  } catch (err) {
    console.error('Failed to initialize dashboard:', err);
  }
});

onUnmounted(() => {
  clearAutoRefresh();
});

const createAuction = () => {
  router.push('/admin-spa/auctions/create');
};

// Lifecycle
onMounted(() => {
  // Load initial data
  // In real app, this would fetch from API
});

// Page meta for router
defineOptions({
  meta: {
    title: 'Dashboard Overview',
    subtitle: 'Monitor your auction platform performance'
  }
});
</script>

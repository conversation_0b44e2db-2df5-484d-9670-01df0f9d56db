<template>
  <div class="admin-file-upload">
    <!-- Upload Area -->
    <div
      :class="[
        'border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer',
        isDragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400',
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      ]"
      @click="triggerFileInput"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <div class="space-y-2">
        <!-- Upload Icon -->
        <div class="mx-auto h-12 w-12 text-gray-400">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
        </div>
        
        <!-- Upload Text -->
        <div>
          <p class="text-sm font-medium text-gray-900">
            {{ uploadText }}
          </p>
          <p class="text-xs text-gray-500">
            {{ supportText }}
          </p>
        </div>
        
        <!-- File Constraints -->
        <div class="text-xs text-gray-400 space-y-1">
          <p v-if="maxSize">Maximum file size: {{ formatFileSize(maxSize * 1024 * 1024) }}</p>
          <p v-if="accept && accept !== '*'">Accepted formats: {{ accept }}</p>
          <p v-if="multiple">You can upload multiple files</p>
        </div>
      </div>
    </div>

    <!-- Hidden File Input -->
    <input
      ref="fileInput"
      type="file"
      :multiple="multiple"
      :accept="accept"
      :disabled="disabled"
      class="hidden"
      @change="handleFileSelect"
    />

    <!-- Upload Progress -->
    <div v-if="uploading" class="mt-4">
      <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
        <span>Uploading files...</span>
        <span>{{ uploadProgress }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${uploadProgress}%` }"
        />
      </div>
    </div>

    <!-- File List -->
    <div v-if="files.length > 0" class="mt-4 space-y-2">
      <h4 class="text-sm font-medium text-gray-900">
        Uploaded Files ({{ files.length }})
      </h4>
      
      <div class="space-y-2 max-h-60 overflow-y-auto">
        <div
          v-for="(file, index) in files"
          :key="file.id || index"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
        >
          <!-- File Info -->
          <div class="flex items-center space-x-3 flex-1 min-w-0">
            <!-- File Icon -->
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
            
            <!-- File Details -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ file.name }}
              </p>
              <div class="flex items-center space-x-2 text-xs text-gray-500">
                <span>{{ formatFileSize(file.size) }}</span>
                <span v-if="file.type">{{ file.type }}</span>
                <span v-if="file.lastModified" class="text-gray-400">
                  {{ formatDate(file.lastModified) }}
                </span>
              </div>
            </div>
          </div>

          <!-- File Actions -->
          <div class="flex items-center space-x-2">
            <!-- Preview Button -->
            <Button
              v-if="canPreview(file)"
              variant="ghost"
              size="sm"
              @click="previewFileMethod(file)"
              class="text-blue-600 hover:text-blue-700"
            >
              Preview
            </Button>
            
            <!-- Download Button -->
            <Button
              v-if="file.url"
              variant="ghost"
              size="sm"
              @click="downloadFile(file)"
              class="text-green-600 hover:text-green-700"
            >
              Download
            </Button>
            
            <!-- Remove Button -->
            <Button
              variant="ghost"
              size="sm"
              @click="removeFile(file, index)"
              :disabled="disabled"
              class="text-red-600 hover:text-red-700"
            >
              Remove
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- File Preview Modal -->
    <Modal v-if="previewFile && showPreview" @close="closePreview">
      <template #header>
        <h3 class="text-lg font-medium">File Preview</h3>
      </template>
      
      <div class="space-y-4">
        <!-- Image Preview -->
        <div v-if="previewFile.type?.startsWith('image/')" class="text-center">
          <img
            :src="getFileUrl(previewFile)"
            :alt="previewFile.name"
            class="max-w-full max-h-96 mx-auto rounded-lg"
          />
        </div>
        
        <!-- Text Preview -->
        <div v-else-if="previewFile.type?.startsWith('text/')" class="bg-gray-50 p-4 rounded-lg">
          <pre class="text-sm text-gray-800 whitespace-pre-wrap">{{ fileContent }}</pre>
        </div>
        
        <!-- Other Files -->
        <div v-else class="text-center py-8">
          <div class="mx-auto h-16 w-16 text-gray-400 mb-4">
            <svg fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
            </svg>
          </div>
          <p class="text-sm text-gray-600">Preview not available for this file type</p>
          <p class="text-xs text-gray-500 mt-1">{{ previewFile.name }}</p>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-3">
          <Button variant="outline" @click="closePreview">Close</Button>
          <Button v-if="previewFile.url" variant="primary" @click="downloadFile(previewFile)">
            Download
          </Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Button, Modal } from '@/components/ui';

interface FileItem {
  id?: string;
  name: string;
  size: number;
  type?: string;
  lastModified?: number;
  url?: string;
  file?: File;
}

interface Props {
  modelValue?: FileItem[];
  multiple?: boolean;
  accept?: string;
  maxSize?: number; // in MB
  disabled?: boolean;
  uploadText?: string;
  supportText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  multiple: false,
  accept: '*',
  maxSize: 10,
  disabled: false,
  uploadText: 'Click to upload or drag and drop',
  supportText: 'Select files from your computer'
});

const emit = defineEmits<{
  'update:modelValue': [files: FileItem[]];
  upload: [files: File[]];
  remove: [file: FileItem, index: number];
  preview: [file: FileItem];
}>();

// State
const fileInput = ref<HTMLInputElement>();
const isDragOver = ref(false);
const uploading = ref(false);
const uploadProgress = ref(0);
const files = ref<FileItem[]>([...props.modelValue]);
const showPreview = ref(false);
const previewFileRef = ref<FileItem | null>(null);
const fileContent = ref('');

// Computed
const previewFile = computed(() => previewFileRef.value);

// Methods
const triggerFileInput = () => {
  if (!props.disabled) {
    fileInput.value?.click();
  }
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files) {
    processFiles(Array.from(target.files));
  }
};

const handleDragOver = (event: DragEvent) => {
  if (!props.disabled) {
    isDragOver.value = true;
  }
};

const handleDragLeave = () => {
  isDragOver.value = false;
};

const handleDrop = (event: DragEvent) => {
  isDragOver.value = false;
  if (!props.disabled && event.dataTransfer?.files) {
    processFiles(Array.from(event.dataTransfer.files));
  }
};

const processFiles = (newFiles: File[]) => {
  const validFiles = newFiles.filter(file => {
    // Check file size
    if (props.maxSize && file.size > props.maxSize * 1024 * 1024) {
      console.warn(`File ${file.name} is too large`);
      return false;
    }
    
    // Check file type
    if (props.accept && props.accept !== '*') {
      const acceptedTypes = props.accept.split(',').map(type => type.trim());
      const isAccepted = acceptedTypes.some(type => {
        if (type.startsWith('.')) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        }
        return file.type.match(type.replace('*', '.*'));
      });
      
      if (!isAccepted) {
        console.warn(`File ${file.name} is not an accepted type`);
        return false;
      }
    }
    
    return true;
  });

  if (validFiles.length > 0) {
    const fileItems: FileItem[] = validFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
      file: file,
      url: URL.createObjectURL(file)
    }));

    if (props.multiple) {
      files.value = [...files.value, ...fileItems];
    } else {
      files.value = fileItems;
    }

    emit('update:modelValue', files.value);
    emit('upload', validFiles);
  }
};

const removeFile = (file: FileItem, index: number) => {
  if (file.url && file.url.startsWith('blob:')) {
    URL.revokeObjectURL(file.url);
  }
  
  files.value.splice(index, 1);
  emit('update:modelValue', files.value);
  emit('remove', file, index);
};

const canPreview = (file: FileItem): boolean => {
  if (!file.type) return false;
  return file.type.startsWith('image/') || file.type.startsWith('text/');
};

const previewFileMethod = async (file: FileItem) => {
  previewFileRef.value = file;
  showPreview.value = true;
  
  if (file.type?.startsWith('text/') && file.file) {
    try {
      fileContent.value = await file.file.text();
    } catch (error) {
      fileContent.value = 'Error reading file content';
    }
  }
  
  emit('preview', file);
};

const closePreview = () => {
  showPreview.value = false;
  previewFileRef.value = null;
  fileContent.value = '';
};

const downloadFile = (file: FileItem) => {
  if (file.url) {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

const getFileUrl = (file: FileItem): string => {
  return file.url || '';
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleDateString();
};
</script>

<style scoped>
/* Custom styles for AdminFileUpload */
.admin-file-upload {
  @apply w-full;
}

/* Drag and drop styles */
.admin-file-upload .upload-area.drag-over {
  @apply border-blue-400 bg-blue-50;
}

/* File list animations */
.admin-file-upload .file-item {
  @apply transition-all duration-200;
}

.admin-file-upload .file-item:hover {
  @apply bg-gray-100;
}

/* Progress bar animation */
.admin-file-upload .progress-bar {
  @apply transition-all duration-300 ease-out;
}
</style>



<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Auction Items</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end align-items-center">
                <!-- Select -->
                <div class="me-3" style="min-width: 200px;">
                    <form id="filter" class="mb-0">
                        <select class="form-select" name="type" onchange="filter.submit()" autocomplete="off">
                            <option <?php if(request()->type == 'available'): ?> selected <?php endif; ?> value="available">Available Items</option>
                            <option <?php if(request()->type == 'sold'): ?> selected <?php endif; ?> value="sold">Sold Items</option>
                        </select>
                    </form>
                </div>
                <!-- End Select -->

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Item::class)): ?>
                <a href="<?php echo e(route('items.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Item
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title">Auction Items</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table js-datatable table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                    <thead class="thead-light">
                        <tr>
                            <th>#</th>
                            <th class="text-left" style="min-width: 80px;">
                                <?php echo app('translator')->get('crud.items.inputs.name'); ?>
                            </th>
                            <th class="text-left">
                                Owner
                            </th>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.items.inputs.auction_type_id'); ?>
                            </th>
                            <th class="text-left">
                                Price
                            </th>
                            <th class="text-left">
                                Date From
                            </th>
                            <th class="text-left">
                                Date To
                            </th>
                            <th class="text-left">
                                Date Added
                            </th>
                            <th class="text-center">
                                <?php echo app('translator')->get('crud.common.actions'); ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="<?php if($item->closed_by): ?> alert alert-soft-warning btn-light <?php endif; ?> " >
                            <td> <?php echo e($key + 1); ?> </td>
                            <td class="table-column-ps-0">
                              <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    <a href="<?php echo e($item->image ?? '-'); ?>" data-fslightbox="gallery">
                                        <img class="img-fluid" style="width: 80px; height: 80px; object-fit: contain;" src="<?php echo e($item->image ?? '-'); ?>" alt="Image Description">
                                    </a>
                                </div>
                                <div>
                                    <a href="/items/<?php echo e($item->id ?? '-'); ?>">
                                        <h5 class="text-inherit mb-0">
                                            <?php echo e($item->name ?? ''); ?>

                                        </h5>
                                        <small class="text-info">
                                            <?php echo e($item->reference_number ?? ''); ?>

                                        </small>
                                    </a>
                                </div>
                              </div>
                            </td>
                            <td>
                                <div><?php echo e(optional($item->user)->name ?? '-'); ?></div>
                                <small class="text-info"><?php echo e($item->status->name ?? '-'); ?></small>
                            </td>
                            <td>
                                <div><?php echo e(optional($item->auctionType)->name ?? '-'); ?></div>
                                <small class="text-info"><?php echo e($item->auctionType->type ?? '-'); ?></small>
                            </td>

                            <td>
                                <?php echo e(_money( $item->target_amount )); ?>

                            </td>
                            <td><?php echo e($item->date_from ?? '-'); ?></td>
                            <td><?php echo e($item->date_to ?? '-'); ?></td>
                            <td><?php echo e($item->created_at ?? '-'); ?></td>

                            <td class="text-center" style="width: 134px;">
                                <div
                                    role="group"
                                    aria-label="Row Actions"
                                    class="btn-group"
                                >

                                    <?php if(!$item->closed_by): ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $item)): ?>
                                    <a href="<?php echo e(route('items.edit', $item)); ?>">
                                        <button type="button" class="btn btn-primary btn-sm m-1">
                                            <i class="bi bi-pencil-square me-1"></i>
                                            Edit
                                        </button>
                                    </a>
                                    <?php endif; ?>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $item)): ?>
                                    <a href="<?php echo e(route('items.show', $item)); ?>">
                                        <button type="button" class="btn btn-info btn-sm m-1 text-white">
                                            <i class="bi bi-eye me-1"></i>
                                            View
                                        </button>
                                    </a>
                                    <?php endif; ?>

                                    <?php if(!$item->closed_by): ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $item)): ?>
                                    <form action="<?php echo e(route('items.destroy', $item)); ?>" method="POST"
                                          onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')">
                                        <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-danger btn-sm m-1">
                                            <i class="bi bi-trash me-1"></i>
                                            Delete
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                    <?php endif; ?>

                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <?php endif; ?>
                    </tbody>

                </table>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>


<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/fslightbox@3.4.1/index.min.js"></script>
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    dataTableBtn(".js-datatable", null, [8, 'desc'])

    // Make sure fslightbox is initialized for image previews
    if (typeof refreshFsLightbox === 'function') {
      refreshFsLightbox();
    }
  });
</script>


<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/items/index.blade.php ENDPATH**/ ?>
<template>
  <div :class="containerClasses">
    <!-- Chart Header -->
    <div v-if="title || subtitle || $slots.header" class="flex items-center justify-between mb-4">
      <div>
        <h3 v-if="title" class="text-lg font-medium text-gray-900">
          {{ title }}
        </h3>
        <p v-if="subtitle" class="text-sm text-gray-600">
          {{ subtitle }}
        </p>
      </div>
      <div class="flex items-center space-x-2">
        <slot name="header" />
        
        <!-- Chart Controls -->
        <div class="flex items-center space-x-2">
          <!-- Time Range Selector -->
          <Select
            v-if="showTimeRange"
            v-model="selectedTimeRange"
            :options="timeRangeOptions"
            size="sm"
            @change="handleTimeRangeChange"
          />
          
          <!-- Chart Type Selector -->
          <Dropdown v-if="showTypeSelector" placement="bottom-end">
            <template #trigger>
              <Button variant="outline" size="sm">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                {{ chartTypeLabels[currentChartType] }}
              </Button>
            </template>
            <div class="py-1">
              <button
                v-for="(label, type) in chartTypeLabels"
                :key="type"
                @click="changeChartType(type)"
                :class="[
                  'block w-full text-left px-4 py-2 text-sm hover:bg-gray-100',
                  currentChartType === type ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                ]"
              >
                {{ label }}
              </button>
            </div>
          </Dropdown>
          
          <!-- Export Button -->
          <Button
            v-if="showExport"
            variant="outline"
            size="sm"
            @click="exportChart"
            :disabled="loading || !hasData"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
          </Button>
          
          <!-- Refresh Button -->
          <Button
            v-if="showRefresh"
            variant="outline"
            size="sm"
            @click="refreshChart"
            :loading="refreshing"
            :disabled="loading"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </Button>
        </div>
      </div>
    </div>

    <!-- Chart Content -->
    <div class="relative">
      <!-- Loading State -->
      <div v-if="loading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
        <Loading size="lg" />
      </div>

      <!-- No Data State -->
      <div v-else-if="!hasData" class="flex flex-col items-center justify-center py-12 text-gray-500">
        <svg class="w-12 h-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <p class="text-sm font-medium">{{ noDataMessage }}</p>
        <p class="text-xs text-gray-400 mt-1">Try adjusting your filters or time range</p>
      </div>

      <!-- Chart Canvas -->
      <div v-else class="relative">
        <canvas
          ref="chartCanvas"
          :width="width"
          :height="height"
          :class="canvasClasses"
        />
        
        <!-- Real-time Indicator -->
        <div v-if="realTime && isConnected" class="absolute top-2 right-2">
          <div class="flex items-center space-x-2 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Live</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Chart Legend -->
    <div v-if="showLegend && hasData && data?.datasets" class="mt-4">
      <div class="flex flex-wrap gap-4">
        <button
          v-for="(dataset, index) in data.datasets"
          :key="index"
          @click="toggleDataset(index)"
          :class="[
            'flex items-center space-x-2 px-3 py-1 rounded-full text-sm transition-colors',
            dataset.hidden ? 'bg-gray-100 text-gray-500' : 'bg-blue-50 text-blue-700'
          ]"
        >
          <div
            :class="[
              'w-3 h-3 rounded-full',
              dataset.hidden ? 'bg-gray-300' : ''
            ]"
            :style="{ backgroundColor: dataset.hidden ? '' : (dataset.backgroundColor || dataset.borderColor) }"
          />
          <span>{{ dataset.label }}</span>
        </button>
      </div>
    </div>

    <!-- Chart Statistics -->
    <div v-if="showStats && hasData" class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
      <div v-for="stat in chartStats" :key="stat.label" class="text-center">
        <div class="text-lg font-semibold text-gray-900">{{ stat.value }}</div>
        <div class="text-sm text-gray-500">{{ stat.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { Select, Dropdown, Button, Loading } from '@/components/ui';

// Chart.js would be imported here in a real implementation
// import { Chart, registerables } from 'chart.js';

interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string;
  borderWidth?: number;
  hidden?: boolean;
  fill?: boolean;
  tension?: number;
}

interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

interface ChartStat {
  label: string;
  value: string | number;
}

interface Props {
  type?: 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter' | 'radar';
  data?: ChartData;
  title?: string;
  subtitle?: string;
  width?: number;
  height?: number;
  responsive?: boolean;
  showLegend?: boolean;
  showTimeRange?: boolean;
  showTypeSelector?: boolean;
  showExport?: boolean;
  showRefresh?: boolean;
  showStats?: boolean;
  loading?: boolean;
  refreshing?: boolean;
  noDataMessage?: string;
  realTime?: boolean;
  options?: Record<string, any>;
  containerClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'line',
  width: 400,
  height: 200,
  responsive: true,
  showLegend: true,
  showTimeRange: false,
  showTypeSelector: false,
  showExport: true,
  showRefresh: false,
  showStats: false,
  loading: false,
  refreshing: false,
  noDataMessage: 'No data available',
  realTime: false,
  options: () => ({}),
  containerClass: ''
});

const emit = defineEmits<{
  'time-range-change': [range: string];
  'type-change': [type: string];
  'data-toggle': [datasetIndex: number];
  'export': [format: string];
  'refresh': [];
}>();

// State
const chartCanvas = ref<HTMLCanvasElement>();
const chartInstance = ref<any>(null);
const currentChartType = ref(props.type);
const selectedTimeRange = ref('7d');
const isConnected = ref(false);

// Computed
const containerClasses = computed(() => {
  return [
    'admin-chart bg-white rounded-lg border border-gray-200 p-6',
    props.containerClass
  ].filter(Boolean).join(' ');
});

const canvasClasses = computed(() => {
  return props.responsive ? 'w-full h-auto' : '';
});

const hasData = computed(() => {
  return props.data && props.data.datasets && props.data.datasets.length > 0;
});

const timeRangeOptions = computed(() => [
  { label: 'Last 24 hours', value: '24h' },
  { label: 'Last 7 days', value: '7d' },
  { label: 'Last 30 days', value: '30d' },
  { label: 'Last 90 days', value: '90d' },
  { label: 'Last year', value: '1y' }
]);

const chartTypeLabels = computed(() => ({
  line: 'Line Chart',
  bar: 'Bar Chart',
  pie: 'Pie Chart',
  doughnut: 'Doughnut Chart',
  area: 'Area Chart',
  scatter: 'Scatter Plot',
  radar: 'Radar Chart'
}));

const chartStats = computed((): ChartStat[] => {
  if (!hasData.value) return [];
  
  const stats: ChartStat[] = [];
  const datasets = props.data!.datasets;
  
  // Calculate total
  const total = datasets.reduce((sum, dataset) => {
    return sum + dataset.data.reduce((dataSum, value) => dataSum + value, 0);
  }, 0);
  
  stats.push({ label: 'Total', value: total.toLocaleString() });
  
  // Calculate average
  const dataPoints = datasets.reduce((count, dataset) => count + dataset.data.length, 0);
  const average = dataPoints > 0 ? total / dataPoints : 0;
  
  stats.push({ label: 'Average', value: average.toFixed(1) });
  
  // Find max value
  const maxValue = Math.max(...datasets.flatMap(dataset => dataset.data));
  stats.push({ label: 'Peak', value: maxValue.toLocaleString() });
  
  // Count data points
  stats.push({ label: 'Data Points', value: dataPoints });
  
  return stats;
});

// Methods
const createChart = async () => {
  if (!chartCanvas.value || !props.data) return;

  // Destroy existing chart
  if (chartInstance.value) {
    chartInstance.value.destroy();
  }

  await nextTick();

  // In a real implementation, you would use Chart.js here
  // chartInstance.value = new Chart(chartCanvas.value, {
  //   type: currentChartType.value,
  //   data: props.data,
  //   options: {
  //     responsive: props.responsive,
  //     maintainAspectRatio: false,
  //     ...props.options
  //   }
  // });

  // Mock chart creation for demo
  console.log('Creating admin chart:', {
    type: currentChartType.value,
    data: props.data,
    options: props.options,
  });
};

const updateChart = () => {
  if (!chartInstance.value || !props.data) return;
  
  chartInstance.value.data = props.data;
  chartInstance.value.update();
};

const handleTimeRangeChange = (range: string) => {
  selectedTimeRange.value = range;
  emit('time-range-change', range);
};

const changeChartType = (type: string) => {
  currentChartType.value = type;
  emit('type-change', type);
  createChart();
};

const toggleDataset = (datasetIndex: number) => {
  if (!chartInstance.value) return;

  const meta = chartInstance.value.getDatasetMeta(datasetIndex);
  meta.hidden = !meta.hidden;
  chartInstance.value.update();
  
  emit('data-toggle', datasetIndex);
};

const exportChart = () => {
  emit('export', 'png');
  
  if (chartInstance.value) {
    const url = chartInstance.value.toBase64Image();
    const link = document.createElement('a');
    link.download = `${props.title || 'chart'}.png`;
    link.href = url;
    link.click();
  }
};

const refreshChart = () => {
  emit('refresh');
};

// Watch for data changes
watch(() => props.data, () => {
  if (chartInstance.value) {
    updateChart();
  } else {
    createChart();
  }
}, { deep: true });

// Watch for type changes
watch(() => props.type, (newType) => {
  currentChartType.value = newType;
  createChart();
});

// Real-time connection simulation
watch(() => props.realTime, (enabled) => {
  if (enabled) {
    // Simulate connection
    setTimeout(() => {
      isConnected.value = true;
    }, 1000);
  } else {
    isConnected.value = false;
  }
});

onMounted(() => {
  createChart();
  
  if (props.realTime) {
    setTimeout(() => {
      isConnected.value = true;
    }, 1000);
  }
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.destroy();
  }
});

// Expose methods for parent component
defineExpose({
  updateChart,
  exportChart,
  getChartData: () => chartInstance.value?.data,
  refreshChart
});
</script>

<style scoped>
.admin-chart {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Real-time indicator animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>

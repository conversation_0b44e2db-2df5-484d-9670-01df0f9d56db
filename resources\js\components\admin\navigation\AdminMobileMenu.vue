<template>
  <Teleport to="body">
    <!-- Mobile Menu Overlay -->
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="adminStore.mobileMenuOpen"
        class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
        @click="adminStore.closeMobileMenu"
      />
    </Transition>

    <!-- Mobile Menu Panel -->
    <Transition
      enter-active-class="transition-transform duration-300"
      enter-from-class="-translate-x-full"
      enter-to-class="translate-x-0"
      leave-active-class="transition-transform duration-300"
      leave-from-class="translate-x-0"
      leave-to-class="-translate-x-full"
    >
      <div
        v-if="adminStore.mobileMenuOpen"
        class="fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-xl lg:hidden"
      >
        <!-- Mobile Menu Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
          <div class="flex items-center space-x-3">
            <!-- Logo -->
            <div class="flex-shrink-0">
              <img
                src="/images/logo.png"
                alt="Vertigo AMS"
                class="h-8 w-auto"
              />
            </div>
            <div>
              <h2 class="text-lg font-semibold text-gray-900">Admin Panel</h2>
              <p class="text-sm text-gray-500">Vertigo AMS</p>
            </div>
          </div>
          
          <!-- Close Button -->
          <button
            @click="adminStore.closeMobileMenu"
            class="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
          >
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Mobile Navigation -->
        <div class="flex-1 overflow-y-auto mobile-scroll">
          <nav class="p-4">
            <div class="space-y-2">
              <!-- Dashboard -->
              <MobileMenuItem
                :item="dashboardItem"
                @navigate="handleNavigation"
              />

              <!-- Sales Section -->
              <MobileMenuItem
                :item="salesItem"
                @navigate="handleNavigation"
              />

              <!-- Auctions Section -->
              <MobileMenuItem
                :item="auctionsItem"
                @navigate="handleNavigation"
              />

              <!-- Items Section -->
              <MobileMenuItem
                :item="itemsItem"
                @navigate="handleNavigation"
              />

              <!-- Users Section -->
              <MobileMenuItem
                :item="usersItem"
                @navigate="handleNavigation"
              />

              <!-- Financial Section -->
              <MobileMenuItem
                :item="financialItem"
                @navigate="handleNavigation"
              />

              <!-- Reports Section -->
              <MobileMenuItem
                :item="reportsItem"
                @navigate="handleNavigation"
              />

              <!-- Settings Section -->
              <MobileMenuItem
                :item="settingsItem"
                @navigate="handleNavigation"
              />
            </div>
          </nav>
        </div>

        <!-- Mobile Menu Footer -->
        <div class="border-t border-gray-200 p-4">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                <svg class="h-6 w-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ authStore.user?.name || 'Admin User' }}
              </p>
              <p class="text-sm text-gray-500 truncate">
                {{ authStore.user?.email || '<EMAIL>' }}
              </p>
            </div>
            <button
              @click="handleLogout"
              class="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
              title="Logout"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAdminStore } from '@/stores/admin';
import { useAuthStore } from '@/stores/auth';
import MobileMenuItem from './MobileMenuItem.vue';

// Stores
const adminStore = useAdminStore();
const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();

// Navigation items (same as AdminNavigation.vue)
const dashboardItem = computed(() => ({
  key: 'dashboard',
  label: 'Dashboard',
  icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z',
  route: '/admin-spa/dashboard',
  active: route.path === '/admin-spa/dashboard' || route.path === '/admin-spa'
}));

const salesItem = computed(() => ({
  key: 'sales',
  label: 'Sales',
  icon: 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z',
  hasChildren: true,
  expanded: adminStore.openMenus.sales,
  children: [
    { key: 'sales-overview', label: 'Sales Overview', route: '/admin-spa/sales' },
    { key: 'sales-orders', label: 'Orders', route: '/admin-spa/sales/orders' },
    { key: 'sales-invoices', label: 'Invoices', route: '/admin-spa/sales/invoices' },
    { key: 'sales-customers', label: 'Customers', route: '/admin-spa/sales/customers' },
    { key: 'sales-reports', label: 'Sales Reports', route: '/admin-spa/sales/reports' }
  ]
}));

const auctionsItem = computed(() => ({
  key: 'auctions',
  label: 'Auctions',
  icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10',
  hasChildren: true,
  expanded: adminStore.openMenus.auctions,
  children: [
    { key: 'auctions-all', label: 'All Auctions', route: '/admin-spa/auctions' },
    { key: 'auctions-create', label: 'Create Auction', route: '/admin-spa/auctions/create' },
    { key: 'auctions-live', label: 'Live Auctions', route: '/admin-spa/auctions/live' },
    { key: 'auctions-ended', label: 'Ended Auctions', route: '/admin-spa/auctions/ended' },
    { key: 'auctions-templates', label: 'Auction Templates', route: '/admin-spa/auctions/templates' }
  ]
}));

const itemsItem = computed(() => ({
  key: 'items',
  label: 'Items',
  icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
  hasChildren: true,
  expanded: adminStore.openMenus.items,
  children: [
    { key: 'items-all', label: 'All Items', route: '/admin-spa/items' },
    { key: 'items-add', label: 'Add Item', route: '/admin-spa/items/create' },
    { key: 'items-categories', label: 'Categories', route: '/admin-spa/items/categories' },
    { key: 'items-bulk', label: 'Bulk Import', route: '/admin-spa/items/bulk-import' },
    { key: 'items-conditions', label: 'Item Conditions', route: '/admin-spa/items/conditions' }
  ]
}));

const usersItem = computed(() => ({
  key: 'users',
  label: 'Users',
  icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z',
  hasChildren: true,
  expanded: adminStore.openMenus.users,
  children: [
    { key: 'users-all', label: 'All Users', route: '/admin-spa/users' },
    { key: 'users-bidders', label: 'Bidders', route: '/admin-spa/users/bidders' },
    { key: 'users-sellers', label: 'Sellers', route: '/admin-spa/users/sellers' },
    { key: 'users-admins', label: 'Administrators', route: '/admin-spa/users/administrators' },
    { key: 'users-roles', label: 'User Roles', route: '/admin-spa/users/roles' },
    { key: 'users-permissions', label: 'Permissions', route: '/admin-spa/users/permissions' }
  ]
}));

const financialItem = computed(() => ({
  key: 'financial',
  label: 'Financial',
  icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
  hasChildren: true,
  expanded: adminStore.openMenus.financial,
  children: [
    { key: 'financial-transactions', label: 'Transactions', route: '/admin-spa/financial/transactions' },
    { key: 'financial-payments', label: 'Payments', route: '/admin-spa/financial/payments' },
    { key: 'financial-commissions', label: 'Commissions', route: '/admin-spa/financial/commissions' },
    { key: 'financial-invoices', label: 'Invoices', route: '/admin-spa/financial/invoices' },
    { key: 'financial-tax', label: 'Tax Reports', route: '/admin-spa/financial/tax-reports' }
  ]
}));

const reportsItem = computed(() => ({
  key: 'reports',
  label: 'Reports',
  icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
  hasChildren: true,
  expanded: adminStore.openMenus.reports,
  children: [
    { key: 'reports-sales', label: 'Sales Reports', route: '/admin-spa/reports/sales' },
    { key: 'reports-analytics', label: 'User Analytics', route: '/admin-spa/reports/analytics' },
    { key: 'reports-performance', label: 'Performance', route: '/admin-spa/reports/performance' },
    { key: 'reports-custom', label: 'Custom Reports', route: '/admin-spa/reports/custom' }
  ]
}));

const settingsItem = computed(() => ({
  key: 'settings',
  label: 'Settings',
  icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
  hasChildren: true,
  expanded: adminStore.openMenus.settings,
  children: [
    { key: 'settings-general', label: 'General', route: '/admin-spa/settings/general' },
    { key: 'settings-auctions', label: 'Auction Settings', route: '/admin-spa/settings/auctions' },
    { key: 'settings-payments', label: 'Payment Gateway', route: '/admin-spa/settings/payments' },
    { key: 'settings-email', label: 'Email Templates', route: '/admin-spa/settings/email' },
    { key: 'settings-logs', label: 'System Logs', route: '/admin-spa/settings/logs' }
  ]
}));

// Methods
const handleNavigation = (route: string) => {
  router.push(route);
  adminStore.closeMobileMenu();
};

const handleLogout = async () => {
  try {
    await authStore.logout();
    router.push('/login');
    adminStore.closeMobileMenu();
  } catch (error) {
    console.error('Logout failed:', error);
  }
};
</script>

<style scoped>
/* Mobile optimizations */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Ensure proper z-index stacking */
.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

/* Better mobile tap highlights */
button {
  -webkit-tap-highlight-color: rgba(0, 104, 255, 0.1);
}

/* Smooth transitions for mobile */
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent body scroll when mobile menu is open */
body.overflow-hidden {
  overflow: hidden;
}
</style>

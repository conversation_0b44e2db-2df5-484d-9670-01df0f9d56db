<template>
  <AdminPageTemplate
    title="Sales Overview"
    subtitle="Monitor sales performance and key metrics"
    :loading="loading"
    :error="error"
  >
    <template #actions>
      <div class="flex space-x-3">
        <Button variant="outline" size="sm" @click="exportData">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"/>
          </svg>
          Export Report
        </Button>
        <Button variant="primary" size="sm" @click="createOrder">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          New Order
        </Button>
      </div>
    </template>

    <!-- Sales Stats -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <AdminStatsCard
        title="Total Sales"
        :value="formatCurrency(stats.totalSales)"
        :change="stats.salesChange"
        icon="revenue"
        color="green"
      />
      <AdminStatsCard
        title="Orders Today"
        :value="stats.ordersToday"
        :change="stats.ordersChange"
        icon="orders"
        color="blue"
      />
      <AdminStatsCard
        title="Active Customers"
        :value="stats.activeCustomers"
        :change="stats.customersChange"
        icon="users"
        color="purple"
      />
      <AdminStatsCard
        title="Avg Order Value"
        :value="formatCurrency(stats.avgOrderValue)"
        :change="stats.avgOrderChange"
        icon="analytics"
        color="indigo"
      />
    </div>

    <!-- Recent Orders -->
    <Card class="mb-8">
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">Recent Orders</h3>
          <Button variant="outline" size="sm" @click="viewAllOrders">
            View All Orders
          </Button>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order ID
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="order in recentOrders" :key="order.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  #{{ order.id }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ order.customer }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatCurrency(order.amount) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <Badge :variant="getStatusVariant(order.status)">
                    {{ order.status }}
                  </Badge>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(order.date) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </Card>

    <!-- Sales Chart Placeholder -->
    <Card>
      <div class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">Sales Trend</h3>
        <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
          <p class="text-gray-500">Sales chart will be implemented here</p>
        </div>
      </div>
    </Card>
  </AdminPageTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { AdminPageTemplate, AdminStatsCard } from '@/components/admin';
import { Button, Card, Badge } from '@/components/ui';

// Router
const router = useRouter();

// State
const loading = ref(false);
const error = ref<string | null>(null);

// Mock data
const stats = ref({
  totalSales: 125430,
  salesChange: 12,
  ordersToday: 24,
  ordersChange: 8,
  activeCustomers: 156,
  customersChange: 5,
  avgOrderValue: 245.50,
  avgOrderChange: -2
});

const recentOrders = ref([
  { id: 1001, customer: 'John Doe', amount: 299.99, status: 'completed', date: new Date() },
  { id: 1002, customer: 'Jane Smith', amount: 149.50, status: 'pending', date: new Date() },
  { id: 1003, customer: 'Bob Johnson', amount: 399.99, status: 'processing', date: new Date() },
  { id: 1004, customer: 'Alice Brown', amount: 199.99, status: 'completed', date: new Date() },
  { id: 1005, customer: 'Charlie Wilson', amount: 89.99, status: 'cancelled', date: new Date() }
]);

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'completed': return 'success';
    case 'processing': return 'info';
    case 'pending': return 'warning';
    case 'cancelled': return 'error';
    default: return 'default';
  }
};

const exportData = () => {
  console.log('Exporting sales data...');
};

const createOrder = () => {
  router.push('/admin-spa/sales/orders');
};

const viewAllOrders = () => {
  router.push('/admin-spa/sales/orders');
};

// Lifecycle
onMounted(() => {
  // Load data
});
</script>

<template>
  <div class="admin-data-table">
    <!-- Table Header with Actions -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
      <div class="flex-1">
        <h3 v-if="title" class="text-lg font-medium text-gray-900">{{ title }}</h3>
        <p v-if="subtitle" class="mt-1 text-sm text-gray-500">{{ subtitle }}</p>
      </div>
      
      <!-- Table Actions -->
      <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
        <!-- Search -->
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <Input
            v-model="searchQuery"
            type="text"
            placeholder="Search..."
            class="pl-10 w-full sm:w-64"
            @input="handleSearch"
          />
        </div>
        
        <!-- Filters -->
        <Button
          v-if="showFilters"
          variant="outline"
          size="sm"
          @click="toggleFilters"
          class="flex items-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
          Filters
          <Badge v-if="activeFiltersCount > 0" variant="primary" size="sm">{{ activeFiltersCount }}</Badge>
        </Button>
        
        <!-- Export -->
        <Button
          v-if="showExport"
          variant="outline"
          size="sm"
          @click="handleExport"
          :loading="exporting"
          class="flex items-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Export
        </Button>
        
        <!-- Refresh -->
        <Button
          variant="outline"
          size="sm"
          @click="handleRefresh"
          :loading="refreshing"
          class="flex items-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </Button>
      </div>
    </div>

    <!-- Filters Panel -->
    <Card v-if="showFilters && filtersVisible" class="mb-6 p-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div v-for="filter in filters" :key="filter.key" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">{{ filter.label }}</label>
          
          <!-- Text Filter -->
          <Input
            v-if="filter.type === 'text'"
            v-model="filterValues[filter.key]"
            :placeholder="filter.placeholder"
            @input="handleFilterChange"
          />
          
          <!-- Select Filter -->
          <Select
            v-else-if="filter.type === 'select'"
            v-model="filterValues[filter.key]"
            :options="filter.options"
            :placeholder="filter.placeholder"
            @change="handleFilterChange"
          />
          
          <!-- Date Range Filter -->
          <div v-else-if="filter.type === 'daterange'" class="flex gap-2">
            <Input
              v-model="filterValues[filter.key + '_from']"
              type="date"
              placeholder="From"
              @change="handleFilterChange"
            />
            <Input
              v-model="filterValues[filter.key + '_to']"
              type="date"
              placeholder="To"
              @change="handleFilterChange"
            />
          </div>
        </div>
      </div>
      
      <div class="mt-4 flex justify-end gap-3">
        <Button variant="outline" size="sm" @click="clearFilters">
          Clear All
        </Button>
        <Button variant="primary" size="sm" @click="applyFilters">
          Apply Filters
        </Button>
      </div>
    </Card>

    <!-- Bulk Actions Bar -->
    <div v-if="showBulkActions && selectedItems.length > 0" class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <span class="text-sm font-medium text-blue-900">
            {{ selectedItems.length }} item{{ selectedItems.length !== 1 ? 's' : '' }} selected
          </span>
          <Button variant="outline" size="sm" @click="clearSelection">
            Clear Selection
          </Button>
        </div>
        
        <div class="flex gap-2">
          <slot name="bulk-actions" :selected-items="selectedItems">
            <Button variant="outline" size="sm" @click="handleBulkDelete" class="text-red-600 hover:text-red-700">
              Delete Selected
            </Button>
          </slot>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-12">
      <Loading size="lg" />
    </div>

    <!-- Error State -->
    <Alert v-else-if="error" variant="error" class="mb-6">
      {{ error }}
    </Alert>

    <!-- Table -->
    <Card v-else class="overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <!-- Table Header -->
          <thead class="bg-gray-50">
            <tr>
              <!-- Select All Checkbox -->
              <th v-if="showBulkActions" class="w-12 px-6 py-3">
                <input
                  type="checkbox"
                  :checked="allSelected"
                  :indeterminate="someSelected"
                  @change="handleSelectAll"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </th>
              
              <!-- Column Headers -->
              <th
                v-for="column in columns"
                :key="column.key"
                :class="[
                  'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                  column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                ]"
                @click="column.sortable ? handleSort(column.key) : null"
              >
                <div class="flex items-center gap-2">
                  <span>{{ column.label }}</span>
                  <div v-if="column.sortable" class="flex flex-col">
                    <svg
                      :class="[
                        'w-3 h-3',
                        sortColumn === column.key && sortOrder === 'asc' ? 'text-blue-600' : 'text-gray-400'
                      ]"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                    </svg>
                  </div>
                </div>
              </th>
              
              <!-- Actions Column -->
              <th v-if="showActions" class="w-20 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          
          <!-- Table Body -->
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="(item, index) in paginatedItems"
              :key="getItemKey(item, index)"
              :class="[
                'hover:bg-gray-50 transition-colors',
                selectedItems.includes(getItemKey(item, index)) ? 'bg-blue-50' : ''
              ]"
            >
              <!-- Select Checkbox -->
              <td v-if="showBulkActions" class="px-6 py-4">
                <input
                  type="checkbox"
                  :checked="selectedItems.includes(getItemKey(item, index))"
                  @change="handleSelectItem(item, index, $event)"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </td>
              
              <!-- Data Columns -->
              <td
                v-for="column in columns"
                :key="column.key"
                class="px-6 py-4 whitespace-nowrap"
              >
                <slot
                  :name="`cell-${column.key}`"
                  :item="item"
                  :value="getNestedValue(item, column.key)"
                  :column="column"
                  :index="index"
                >
                  <div :class="getCellClasses(column)">
                    {{ formatCellValue(getNestedValue(item, column.key), column) }}
                  </div>
                </slot>
              </td>
              
              <!-- Actions Column -->
              <td v-if="showActions" class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <slot name="actions" :item="item" :index="index">
                  <div class="flex justify-end gap-2">
                    <Button variant="ghost" size="sm" @click="handleView(item)">
                      View
                    </Button>
                    <Button variant="ghost" size="sm" @click="handleEdit(item)">
                      Edit
                    </Button>
                    <Button variant="ghost" size="sm" @click="handleDelete(item)" class="text-red-600 hover:text-red-700">
                      Delete
                    </Button>
                  </div>
                </slot>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- Empty State -->
      <div v-if="!loading && paginatedItems.length === 0" class="text-center py-12">
        <div class="mx-auto h-12 w-12 text-gray-400">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="mt-2 text-sm font-medium text-gray-900">{{ emptyStateTitle }}</h3>
        <p class="mt-1 text-sm text-gray-500">{{ emptyStateMessage }}</p>
        <div class="mt-6">
          <slot name="empty-actions">
            <Button variant="primary" @click="$emit('create')">
              {{ createButtonText }}
            </Button>
          </slot>
        </div>
      </div>
    </Card>

    <!-- Pagination -->
    <div v-if="showPagination && totalPages > 1" class="mt-6">
      <Pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        :total-items="totalItems"
        :per-page="perPage"
        @page-change="handlePageChange"
        @per-page-change="handlePerPageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive, onMounted } from 'vue';
import { Button, Input, Card, Select, Loading, Alert, Badge, Pagination } from '@/components/ui';

// Types
interface Column {
  key: string;
  label: string;
  sortable?: boolean;
  type?: 'text' | 'number' | 'date' | 'currency' | 'badge' | 'custom';
  format?: (value: any) => string;
  align?: 'left' | 'center' | 'right';
  width?: string;
}

interface Filter {
  key: string;
  label: string;
  type: 'text' | 'select' | 'daterange';
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
}

interface Props {
  title?: string;
  subtitle?: string;
  items?: any[];
  columns?: Column[];
  filters?: Filter[];
  loading?: boolean;
  error?: string | null;
  showFilters?: boolean;
  showBulkActions?: boolean;
  showActions?: boolean;
  showExport?: boolean;
  showPagination?: boolean;
  currentPage?: number;
  totalPages?: number;
  totalItems?: number;
  perPage?: number;
  sortColumn?: string;
  sortOrder?: 'asc' | 'desc';
  selectedItems?: any[];
  emptyStateTitle?: string;
  emptyStateMessage?: string;
  createButtonText?: string;
  itemKey?: string;
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  columns: () => [],
  filters: () => [],
  loading: false,
  error: null,
  showFilters: true,
  showBulkActions: false,
  showActions: true,
  showExport: true,
  showPagination: true,
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  perPage: 20,
  sortColumn: '',
  sortOrder: 'asc',
  selectedItems: () => [],
  emptyStateTitle: 'No data found',
  emptyStateMessage: 'There are no items to display.',
  createButtonText: 'Create New',
  itemKey: 'id'
});

const emit = defineEmits<{
  search: [query: string];
  sort: [column: string, order: 'asc' | 'desc'];
  filter: [filters: Record<string, any>];
  'page-change': [page: number];
  'per-page-change': [perPage: number];
  'select-all': [selected: boolean];
  'select-item': [item: any, selected: boolean];
  'bulk-delete': [items: any[]];
  view: [item: any];
  edit: [item: any];
  delete: [item: any];
  create: [];
  export: [];
  refresh: [];
}>();

// State
const searchQuery = ref('');
const filtersVisible = ref(false);
const filterValues = reactive<Record<string, any>>({});
const selectedItemsLocal = ref<any[]>([...props.selectedItems]);
const refreshing = ref(false);
const exporting = ref(false);

// Initialize filter values
onMounted(() => {
  props.filters.forEach(filter => {
    if (filter.type === 'daterange') {
      filterValues[filter.key + '_from'] = '';
      filterValues[filter.key + '_to'] = '';
    } else {
      filterValues[filter.key] = '';
    }
  });
});

// Computed
const paginatedItems = computed(() => {
  if (props.showPagination) {
    return props.items;
  }
  return props.items;
});

const allSelected = computed(() => {
  return paginatedItems.value.length > 0 &&
         paginatedItems.value.every(item => selectedItemsLocal.value.includes(getItemKey(item, 0)));
});

const someSelected = computed(() => {
  return selectedItemsLocal.value.length > 0 && !allSelected.value;
});

const activeFiltersCount = computed(() => {
  return Object.values(filterValues).filter(value => value && value !== '').length;
});

// Methods
const getItemKey = (item: any, index: number): any => {
  return item[props.itemKey] || index;
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

const formatCellValue = (value: any, column: Column): string => {
  if (value === null || value === undefined) return '';

  if (column.format) {
    return column.format(value);
  }

  switch (column.type) {
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(value);
    case 'date':
      return new Date(value).toLocaleDateString();
    case 'number':
      return value.toLocaleString();
    default:
      return String(value);
  }
};

const getCellClasses = (column: Column): string => {
  const baseClasses = 'text-sm text-gray-900';
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  return `${baseClasses} ${alignClasses[column.align || 'left']}`;
};

const handleSearch = () => {
  emit('search', searchQuery.value);
};

const handleSort = (column: string) => {
  const newOrder = props.sortColumn === column && props.sortOrder === 'asc' ? 'desc' : 'asc';
  emit('sort', column, newOrder);
};

const handleFilterChange = () => {
  // Debounce filter changes
  setTimeout(() => {
    emit('filter', { ...filterValues });
  }, 300);
};

const toggleFilters = () => {
  filtersVisible.value = !filtersVisible.value;
};

const clearFilters = () => {
  Object.keys(filterValues).forEach(key => {
    filterValues[key] = '';
  });
  emit('filter', {});
};

const applyFilters = () => {
  emit('filter', { ...filterValues });
};

const handleSelectAll = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.checked) {
    selectedItemsLocal.value = paginatedItems.value.map((item, index) => getItemKey(item, index));
  } else {
    selectedItemsLocal.value = [];
  }
  emit('select-all', target.checked);
};

const handleSelectItem = (item: any, index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const itemKey = getItemKey(item, index);

  if (target.checked) {
    if (!selectedItemsLocal.value.includes(itemKey)) {
      selectedItemsLocal.value.push(itemKey);
    }
  } else {
    selectedItemsLocal.value = selectedItemsLocal.value.filter(key => key !== itemKey);
  }

  emit('select-item', item, target.checked);
};

const clearSelection = () => {
  selectedItemsLocal.value = [];
  emit('select-all', false);
};

const handleBulkDelete = () => {
  const selectedItems = paginatedItems.value.filter((item, index) =>
    selectedItemsLocal.value.includes(getItemKey(item, index))
  );
  emit('bulk-delete', selectedItems);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handlePerPageChange = (perPage: number) => {
  emit('per-page-change', perPage);
};

const handleView = (item: any) => {
  emit('view', item);
};

const handleEdit = (item: any) => {
  emit('edit', item);
};

const handleDelete = (item: any) => {
  emit('delete', item);
};

const handleExport = async () => {
  exporting.value = true;
  try {
    emit('export');
  } finally {
    exporting.value = false;
  }
};

const handleRefresh = async () => {
  refreshing.value = true;
  try {
    emit('refresh');
  } finally {
    refreshing.value = false;
  }
};

// Watch for prop changes
watch(() => props.selectedItems, (newItems) => {
  selectedItemsLocal.value = [...newItems];
});
</script>

<style scoped>
/* Custom styles for AdminDataTable */
.admin-data-table {
  @apply w-full;
}

/* Checkbox indeterminate state */
input[type="checkbox"]:indeterminate {
  @apply bg-blue-600 border-blue-600;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M5.707 7.293a1 1 0 0 0-1.414 1.414l2 2a1 1 0 0 0 1.414 0l4-4a1 1 0 0 0-1.414-1.414L7 8.586 5.707 7.293z'/%3e%3c/svg%3e");
}

/* Table hover effects */
.admin-data-table tbody tr:hover {
  @apply bg-gray-50;
}

/* Sort indicator animations */
.admin-data-table th svg {
  @apply transition-colors duration-150;
}

/* Filter panel animations */
.admin-data-table .filters-panel {
  @apply transition-all duration-300 ease-in-out;
}

/* Bulk actions bar */
.admin-data-table .bulk-actions-bar {
  @apply transition-all duration-300 ease-in-out;
  animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.admin-data-table .loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10;
}

/* Responsive table */
@media (max-width: 768px) {
  .admin-data-table table {
    @apply text-sm;
  }

  .admin-data-table th,
  .admin-data-table td {
    @apply px-3 py-2;
  }
}

/* Custom scrollbar for table */
.admin-data-table .overflow-x-auto::-webkit-scrollbar {
  @apply h-2;
}

.admin-data-table .overflow-x-auto::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.admin-data-table .overflow-x-auto::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded hover:bg-gray-400;
}
</style>

<template>
  <AdminPageTemplate
    title="Sales Reports"
    subtitle="Analyze sales performance and trends"
    :loading="loading"
    :error="error"
  >
    <template #actions>
      <div class="flex space-x-3">
        <Button variant="outline" size="sm" @click="exportReport">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"/>
          </svg>
          Export Report
        </Button>
        <Button variant="outline" size="sm" @click="scheduleReport">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          Schedule
        </Button>
        <Button variant="primary" size="sm" @click="generateReport">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          Generate Report
        </Button>
      </div>
    </template>

    <!-- Report Filters -->
    <Card class="mb-8">
      <div class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">Report Filters</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
            <Select
              v-model="filters.dateRange"
              :options="dateRangeOptions"
              @change="updateReport"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
            <Select
              v-model="filters.reportType"
              :options="reportTypeOptions"
              @change="updateReport"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Group By</label>
            <Select
              v-model="filters.groupBy"
              :options="groupByOptions"
              @change="updateReport"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <Select
              v-model="filters.status"
              :options="statusOptions"
              @change="updateReport"
            />
          </div>
        </div>
      </div>
    </Card>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <AdminStatsCard
        title="Total Revenue"
        :value="formatCurrency(metrics.totalRevenue)"
        :change="metrics.revenueChange"
        icon="revenue"
        color="green"
      />
      <AdminStatsCard
        title="Total Orders"
        :value="metrics.totalOrders"
        :change="metrics.ordersChange"
        icon="orders"
        color="blue"
      />
      <AdminStatsCard
        title="Avg Order Value"
        :value="formatCurrency(metrics.avgOrderValue)"
        :change="metrics.avgOrderChange"
        icon="analytics"
        color="indigo"
      />
      <AdminStatsCard
        title="Conversion Rate"
        :value="metrics.conversionRate + '%'"
        :change="metrics.conversionChange"
        icon="analytics"
        color="purple"
      />
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Sales Trend Chart -->
      <Card>
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-6">Sales Trend</h3>
          <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <p class="text-gray-500">Sales trend chart will be implemented here</p>
          </div>
        </div>
      </Card>

      <!-- Top Products Chart -->
      <Card>
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-6">Top Products</h3>
          <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <p class="text-gray-500">Top products chart will be implemented here</p>
          </div>
        </div>
      </Card>
    </div>

    <!-- Detailed Report Table -->
    <Card>
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">Detailed Report</h3>
          <div class="flex space-x-2">
            <Button variant="outline" size="sm" @click="refreshData">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
              Refresh
            </Button>
          </div>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Period
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Orders
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Revenue
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Avg Order
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Growth
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="row in reportData" :key="row.period">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ row.period }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ row.orders }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatCurrency(row.revenue) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatCurrency(row.avgOrder) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getGrowthClass(row.growth)">
                    {{ row.growth >= 0 ? '+' : '' }}{{ row.growth }}%
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </Card>
  </AdminPageTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { AdminPageTemplate, AdminStatsCard } from '@/components/admin';
import { Button, Card, Select } from '@/components/ui';

// State
const loading = ref(false);
const error = ref<string | null>(null);

// Filters
const filters = ref({
  dateRange: 'month',
  reportType: 'sales',
  groupBy: 'day',
  status: 'all'
});

// Filter options
const dateRangeOptions = [
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' },
  { value: 'year', label: 'This Year' },
  { value: 'custom', label: 'Custom Range' }
];

const reportTypeOptions = [
  { value: 'sales', label: 'Sales Report' },
  { value: 'products', label: 'Product Performance' },
  { value: 'customers', label: 'Customer Analysis' },
  { value: 'geographic', label: 'Geographic Report' }
];

const groupByOptions = [
  { value: 'day', label: 'Daily' },
  { value: 'week', label: 'Weekly' },
  { value: 'month', label: 'Monthly' },
  { value: 'quarter', label: 'Quarterly' }
];

const statusOptions = [
  { value: 'all', label: 'All Orders' },
  { value: 'completed', label: 'Completed Only' },
  { value: 'pending', label: 'Pending Only' },
  { value: 'cancelled', label: 'Cancelled Only' }
];

// Mock data
const metrics = ref({
  totalRevenue: 125430,
  revenueChange: 12,
  totalOrders: 342,
  ordersChange: 8,
  avgOrderValue: 366.75,
  avgOrderChange: 4,
  conversionRate: 3.2,
  conversionChange: 0.5
});

const reportData = ref([
  { period: 'Week 1', orders: 85, revenue: 31250, avgOrder: 367.65, growth: 12 },
  { period: 'Week 2', orders: 92, revenue: 33780, avgOrder: 367.17, growth: 8 },
  { period: 'Week 3', orders: 78, revenue: 28600, avgOrder: 366.67, growth: -15 },
  { period: 'Week 4', orders: 87, revenue: 31800, avgOrder: 365.52, growth: 11 }
]);

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const getGrowthClass = (growth: number) => {
  return growth >= 0 ? 'text-green-600' : 'text-red-600';
};

const updateReport = () => {
  console.log('Updating report with filters:', filters.value);
  // Implement report update logic
};

const generateReport = () => {
  console.log('Generating new report...');
  // Implement report generation
};

const exportReport = () => {
  console.log('Exporting report...');
  // Implement report export
};

const scheduleReport = () => {
  console.log('Scheduling report...');
  // Implement report scheduling
};

const refreshData = () => {
  console.log('Refreshing report data...');
  // Implement data refresh
};

// Lifecycle
onMounted(() => {
  // Load initial report data
});
</script>



<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-md-3">
            <h1 class="page-header-title mb-0">Bid List</h1>
        </div>
        <div class="col-md-9">
            <div class="d-flex justify-content-end align-items-center">
                <form id="filter" class="d-flex align-items-center me-3 mb-0">
                    <div class="me-3" style="min-width: 200px;">
                        <select class="form-select form-select-sm" name="item_id" onchange="filter.submit()" autocomplete="off">
                            <option value="0">All Items</option>
                            <?php $__currentLoopData = App\Models\Item::whereNull('closed_by')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option <?php if(request()->item_id == $item->id): ?> selected <?php endif; ?> value="<?php echo e($item->id); ?>"><?php echo e($item->name ?? ''); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div style="min-width: 150px;">
                        <select class="form-select form-select-sm" name="status" onchange="filter.submit()" autocomplete="off">
                            <option value="0">All Status</option>
                            <?php $__currentLoopData = ['open', 'closed']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option <?php if(request()->status == $status): ?> selected <?php endif; ?> value="<?php echo e($status); ?>"><?php echo e(_formatText($status)); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </form>
                
                <a href="<?php echo e(route('auctions.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Bid
                </a>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title">Bids</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table js-datatable table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                    <thead class="thead-light">
                        <tr>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.auctions.inputs.item_id'); ?>
                            </th>      
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.auctions.inputs.name'); ?>
                            </th>                    
                            <th class="text-left">
                                Auction Listing
                            </th>
                            <th class="text-right">
                                Bid <?php echo app('translator')->get('crud.auctions.inputs.amount'); ?>
                            </th>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.auctions.inputs.description'); ?>
                            </th>
                            <th class="text-center">
                                <?php echo app('translator')->get('crud.common.actions'); ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $auctions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $auction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="<?php if($auction->item->closed_by): ?> bg-soft-warning <?php endif; ?>">
                            <td class="table-column-ps-0">
                                <?php if($auction->item): ?>
                                    <?php $item = $auction->item ?>
                                    <a class="d-flex align-items-center" href="/auctions/<?php echo e($auction->id); ?>">  <!-- href="/items/<?php echo e($item->id ?? '-'); ?> -->
                                        <div class="flex-shrink-0 ms-3">
                                          <img class="avatar avatar-lg" src="<?php echo e($item->image ?? '-'); ?>" alt="Image Description" width="80" height="80" style="object-fit: contain;">
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                          <h5 class="text-inherit mb-0">
                                              <?php echo e($item->name ?? '-'); ?>

                                          </h5>
                                        </div>
                                    </a>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($auction->user->name ?? '-'); ?></td>
                            <td><?php echo e(optional($auction->auctionType)->name ?? '-'); ?></td>
                            <td class="text-end"><?php echo e(_money($auction->bid_amount) ?? '-'); ?></td>
                            <td><?php echo e($auction->description ?? '-'); ?></td>
                            <td class="text-center" style="width: 200px;">
                                <?php if($auction->item->closed_by): ?>
                                    <?php if($auction->closed_by && !$auction->tagged_by): ?>
                                    <div class="d-flex justify-content-center">
                                        <a href="#" class="btn btn-primary btn-sm" onclick="payAuctionApp.getAuction('<?php echo e($auction->id); ?>')" data-toggle="modal" data-target=".pay-bid-modal">
                                            <i class="bi bi-credit-card me-1"></i> Add Payment
                                        </a>
                                    </div>
                                    <?php endif; ?>
                                    <span class="badge bg-success mt-2">Accepted</span>
                                <?php else: ?>
                                    <div class="d-flex justify-content-center gap-2">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $auction)): ?>
                                        <a href="/accept-bid/<?php echo e($auction->id); ?>" onclick="return confirm('Are you sure. \n You wanted to accept this bid as a winner?.')">
                                            <button type="button" class="btn btn-success btn-sm">
                                                <i class="bi bi-check-circle me-1"></i> Accept
                                            </button>
                                        </a>
                                        <?php endif; ?> 

                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $auction)): ?>
                                        <a href="/auctions/<?php echo e($auction->id); ?>">
                                            <button type="button" class="btn btn-info btn-sm text-white">
                                                <i class="bi bi-eye me-1"></i> View
                                            </button>
                                        </a>
                                        <?php endif; ?> 

                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $auction)): ?>
                                        <form action="<?php echo e(route('auctions.destroy', $auction)); ?>" method="POST" 
                                              onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')">
                                            <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="bi bi-x-circle me-1"></i> Cancel
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="text-center p-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="bi bi-inbox fs-1 text-muted mb-2"></i>
                                    <p class="mb-0">No bids found</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    dataTableBtn()
  });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/auctions/index.blade.php ENDPATH**/ ?>
import { createApp } from 'vue';
import { createP<PERSON> } from 'pinia';
import router from '@/router/admin';
import '../css/app.css';

// Import global components
import {
  Button,
  Input,
  Card,
  Modal,
  Select,
  Loading,
  Container,
  Badge,
  Alert,
  Pagination,
  NotificationContainer
} from '@/components/ui';

// Import admin components
import {
  AdminLayout,
  AdminSidebar,
  AdminHeader,
  AdminContainer,
  AdminNavigation,
  AdminMenuItem,
  AdminMobileMenu,
  MobileMenuItem
} from '@/components/admin';

// Import stores for initialization
import { useAuthStore } from '@/stores/auth';
import { useAdminStore } from '@/stores/admin';
import { useNotificationsStore } from '@/stores/notifications';

// Create root component
const RootComponent = {
  template: `
    <div class="min-h-screen bg-gray-100">
      <router-view />
      <NotificationContainer />
    </div>
  `,
  setup() {
    const notificationsStore = useNotificationsStore();
    const authStore = useAuthStore();
    const adminStore = useAdminStore();

    // Initialize stores on app startup
    const initializeApp = async () => {
      try {
        // Show loading state
        console.log('Initializing admin application...');

        // Initialize auth store first
        await authStore.initialize();
        console.log('Auth store initialized');

        // Verify user has admin access
        if (authStore.user) {
          const user = authStore.user as any;
          const hasAdminAccess = !user.isCustomer?.() && !user.isSupplier?.();

          if (!hasAdminAccess) {
            console.warn('User does not have admin access');
            notificationsStore.warning('You do not have permission to access the admin panel');
            // Redirect to home page
            setTimeout(() => {
              window.location.href = '/';
            }, 2000);
            return;
          }
        }

        // Initialize admin store
        adminStore.initialize();
        console.log('Admin store initialized');

        // Show welcome message
        notificationsStore.success('Admin panel loaded successfully!');
        console.log('Admin application initialized successfully');
      } catch (error) {
        console.error('Failed to initialize admin app:', error);
        notificationsStore.error('Failed to initialize admin panel. Please refresh the page.');

        // Log detailed error information
        if (error instanceof Error) {
          console.error('Error details:', {
            message: error.message,
            stack: error.stack,
            name: error.name
          });
        }
      }
    };

    // Initialize on mount
    initializeApp();

    return {};
  }
};

// Create Vue app
const app = createApp(RootComponent);

// Create Pinia store
const pinia = createPinia();

// Use plugins
app.use(pinia);
app.use(router);

// Register global UI components
app.component('Button', Button);
app.component('Input', Input);
app.component('Card', Card);
app.component('Modal', Modal);
app.component('Select', Select);
app.component('Loading', Loading);
app.component('Container', Container);
app.component('Badge', Badge);
app.component('Alert', Alert);
app.component('Pagination', Pagination);
app.component('NotificationContainer', NotificationContainer);

// Register admin components
app.component('AdminLayout', AdminLayout);
app.component('AdminSidebar', AdminSidebar);
app.component('AdminHeader', AdminHeader);
app.component('AdminContainer', AdminContainer);
app.component('AdminNavigation', AdminNavigation);
app.component('AdminMenuItem', AdminMenuItem);
app.component('AdminMobileMenu', AdminMobileMenu);
app.component('MobileMenuItem', MobileMenuItem);

// Global error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Admin Vue error:', err, info);

  // Show user-friendly error message
  try {
    const notificationsStore = useNotificationsStore();
    notificationsStore.error('An unexpected error occurred. Please refresh the page.');
  } catch (storeError) {
    console.error('Failed to show error notification:', storeError);
    // Fallback to alert if store is not available
    alert('An unexpected error occurred. Please refresh the page.');
  }
};

// Global warning handler
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Admin Vue warning:', msg, trace);
};

// Mount the app with error handling
try {
  app.mount('#admin-app');
  console.log('Admin app mounted successfully');
} catch (error) {
  console.error('Failed to mount admin app:', error);

  // Show fallback error message
  const adminAppElement = document.getElementById('admin-app');
  if (adminAppElement) {
    adminAppElement.innerHTML = `
      <div class="min-h-screen bg-gray-100 flex items-center justify-center">
        <div class="text-center">
          <div class="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 class="text-2xl font-bold text-gray-900 mb-2">Failed to Load Admin Panel</h1>
          <p class="text-gray-600 mb-4">There was an error loading the admin interface.</p>
          <button
            onclick="window.location.reload()"
            class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Refresh Page
          </button>
        </div>
      </div>
    `;
  }
}

// Export for global access if needed
export { app, pinia, router };

// Add global properties for backward compatibility
declare global {
  interface Window {
    adminApp?: typeof app;
    user?: any;
  }
}

// Store app instance globally for debugging
window.adminApp = app;

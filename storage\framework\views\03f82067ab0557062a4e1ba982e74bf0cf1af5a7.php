<?php $editing = isset($order) ?>

<div class="" id="order-el">
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <!-- Left Section - Input Fields and Table -->
            <div class="col-lg-8">
              <!-- Top Input Fields Side by Side -->
              <div class="row mb-4">
                <div class="col-sm-6">
                  <h2>Add Item:</h2>
                  <select class="form-control js-select form-select fs-4" 
                          @change="addItem($event)" 
                          data-hs-tom-select-options='{
                            "placeholder": "Select Item..."
                          }'
                          style="height: 55px; font-size: 1.25rem; padding: 0.5rem 1rem; width: 100%;">
                    <option value="" selected disabled>Select new Item</option>
                    <option v-for="(item, i) in items" :value="item.id" v-text="getName(item)"></option>
                  </select>
                </div>
                <div class="col-sm-6">
                  <h2>Customer:</h2>
                  <select class="js-select form-select form-control" 
                          autocomplete="on" 
                          name="user_id" 
                          required 
                          style="height: 50px; font-size: 1.1rem;"
                          data-hs-tom-select-options='{
                            "create": true,
                            "placeholder": "Create New..."
                          }'>
                    <?php $selected = $editing && $order->user_id ? $order->user_id: '' ?>
                    <option value="2" selected>Walkin customer</option>
                    <?php $__currentLoopData = App\Models\User::get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($user->id); ?>" <?php echo e($user->id == $selected ? 'selected' : ''); ?>>
                      <?php echo e($user->name ?? ''); ?>

                    </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </select>
                </div>
              </div>
              
              <!-- Table -->
              <div class="table-responsive" style="min-height: 300px;">
                <table class="table table-borderless table-nowrap table-align-middle">
                  <thead class="thead-light">
                    <tr>
                      <th class="">Item</th>
                      <th class="">Discount</th>
                      <th class="">Selling Price</th>
                      <th class="table-text-end">Amount</th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(sale, index) in order.sales">
                      <td> 
                        <span class="d-block fs-4 fw-semibold mb-0" v-text="getName(sale.item)"></span>
                      </td>                
                      <td>
                        <input type="number" name="discounts[]" min="0" step="0.00" class="form-control w-100 fw-semibold fs-4" v-model.lazy="sale.discount" @change="calculate" :max="sale?.amount" required> 
                        <input type="hidden" :value="order.discount" name="discount">
                      </td>
                      <td><span class="fw-semibold fs-4" v-text="formatNumber(sale.selling_price)"></span></td>
                      <td class="table-text-end fw-semibold fs-4"><span v-text="formatNumber(sale?.selling_price * sale.quantity)"></span></td>
                      <td><span @click="removeItem(index)" class="btn btn-outline-danger btn-sm">del</span></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            
            <!-- Right Section - Calculation Summary -->
            <div class="col-lg-4">
              <div class="border rounded p-4 bg-light">
                <h3 class="mb-4">Order Summary</h3>
                <dl class="row">
                  <dt class="col-6 fs-3">Subtotal:</dt>
                  <dd class="col-6 fs-3 text-end"> 
                    <span v-text="formatNumber(order.sub_total)"></span>
                    <input type="hidden" name="sub_total" v-model.lazy="order.sub_total">
                  </dd>

                  <dt class="col-6 fs-3">Total:</dt>
                  <dd class="col-6 fs-3 text-end">
                    <span v-text="formatNumber(order.amount_total)"></span>
                    <input type="hidden" name="amount_total" v-model.lazy="order.amount_total">
                  </dd>

                  <dt class="col-6 fs-3">Discount:</dt>
                  <dd class="col-6 fs-3 text-end">
                    <span v-text="formatNumber(order.discount)"></span>
                  </dd>

                  <dt class="col-6 fs-3">Amount paid:</dt>
                  <dd class="col-6 fs-3">
                    <input type="number" step="0.01" v-model.lazy="order.amount_paid" class="form-control fw-semibold fs-3 text-danger" name="amount_paid" style="height: 50px; font-size: 1.1rem;"/>
                  </dd>
                </dl>
                
                <!-- Action Buttons -->
                <div v-if="isReady" class="d-grid gap-2 mt-4">
                  <button class="btn btn-dark btn-lg" type="submit">
                    <i class="bi-save me-1"></i> Save and Exit
                  </button>
                </div>
                <div v-if="!isReady" class="d-grid gap-2 mt-4">
                  <span class="btn btn-dark btn-lg" @click="getReady" type="button">
                    <i class="bi-calculator me-1"></i> Calculate
                  </span>     
                </div>
              </div>
            </div>
          </div>
          
          <!-- Hidden field for order data -->
          <textarea name="order" :value="JSON.stringify(order)" style="opacity:0; width: 0; margin: 0;"></textarea>
        </div>
      </div>
    </div>
  </div>
</div>

<?php $__env->startPush('scripts'); ?>

  <script type="text/javascript">
    
    new Vue({
      el: "#order-el",
      data(){
        return{
          isReady:false,
          order: {
            sales: [], 
            sub_total: 0,
            amount_total: 0,
            amount_paid: 0,
            supplier_id: null,
            discount: 0,
            vat: 0,
          },
          vat: <?php echo json_encode( auth()->user()->getGlobal('vat') ?? 0, 15, 512) ?>,
          units: [],
          items: <?php echo json_encode( $items, 15, 512) ?>,
        }
      },

      methods: {

        getorder(){
          if( ! this.order.id) { return false; }
          axios.get('/order-ajax/' + this.order.id).then( res => {
            this.order = res.data;
          });
        },

        save(){
          axios.post('/save-order', this,order).then( res => {
            this.order = res.data;
          });
        },

        removeItem(index){
          this.order.sales.splice(index, 1);
          this.calculate();
        },


        getReady(){
          this.order.sub_total = 0;
          this.order.amount_total = 0;
          this.order.discount = 0;
          // this.order.vat = 0;

          for( var sale of this.order.sales){
            var amount = eval(sale.quantity * sale.selling_price );
            this.order.discount += Number( sale.discount );
            this.order.sub_total +=  eval(sale.quantity * sale.selling_price);
            // if( sale.vat > 0 ) {
            //   this.order.vat +=  eval( amount * sale.vat / 100 );
            // }
          }

          this.order.amount_total = eval(this.order.sub_total - this.order.discount ).toFixed(2);
          this.order.sub_total = this.order.sub_total.toFixed(2);
          this.order.discount = this.order.discount.toFixed(2);
          // this.order.vat = this.order.vat.toFixed(2);
          this.order.amount_paid = this.order.amount_total;
          this.isReady = true;
        },

        addItem(e){

          var item_id = e.target.value;
          var check = this.order.sales.filter( item => item.item_id == item_id);
          if( ! item_id || check.length > 0 ) return;

          var items = this.items.filter( item => item.id == item_id);
          if( items.length > 0) {
            var item = items[0];
            var sale = {};
            sale.item = item;
            sale.quantity = 1;
            sale.selling_price = item.target_amount;
            sale.item_id  = item.id;
            sale.discount    = 0;
            sale.vat   = item.vat_applied ? this.vat : 0 ;
            this.order.sales.push(sale);
            $(e.target).val("");
          }
          this.calculate();
        },

        formatNumber (num) {
            return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
        },

        calculate(){
          this.isReady = false;
        },

        getName(item) {
          var name = item.name;
          if(item.reference_number) {
            name += ' ~ ' + item.reference_number;
          }
          return name;
        }


      },


      created(){
          var order = <?php echo json_encode( isset($order) ? $order : null , 15, 512) ?>;
          if( order){
            this.order = order;
            this.getReady();
          }

      }

    });

    $(document).on("keydown", "form", function(event) { 
        return event.key != "Enter";
    });

  </script>

<?php $__env->stopPush(); ?>

<style>
/* Custom styles for select elements */
.tom-select-custom .ts-control {
  height: 50px !important;
  font-size: 1.1rem !important;
  padding: 10px 15px !important;
}

.tom-select-custom .ts-dropdown {
  font-size: 1.1rem !important;
}

.tom-select-custom .ts-dropdown .option {
  padding: 10px 15px !important;
}
</style>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/orders/form-inputs.blade.php ENDPATH**/ ?>
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// General Settings Interface
export interface GeneralSettings {
  site_name: string;
  site_description: string;
  site_url: string;
  admin_email: string;
  support_email: string;
  phone: string;
  address: string;
  timezone: string;
  date_format: string;
  time_format: string;
  currency: string;
  currency_symbol: string;
  language: string;
  maintenance_mode: boolean;
  maintenance_message: string;
  google_analytics_id?: string;
  facebook_pixel_id?: string;
}

// Auction Settings Interface
export interface AuctionSettings {
  default_auction_duration: number;
  min_bid_increment: number;
  max_bid_increment: number;
  auto_extend_time: number;
  auto_extend_trigger: number;
  allow_proxy_bidding: boolean;
  require_registration: boolean;
  registration_approval: boolean;
  bid_deposit_required: boolean;
  bid_deposit_percentage: number;
  commission_rate: number;
  buyer_premium: number;
  seller_commission: number;
  payment_deadline_days: number;
  pickup_deadline_days: number;
  late_payment_fee: number;
  default_reserve_price: number;
  allow_reserve_price: boolean;
  show_reserve_met: boolean;
  show_bidder_names: boolean;
  show_bid_history: boolean;
  email_notifications: boolean;
  sms_notifications: boolean;
}

// Payment Settings Interface
export interface PaymentSettings {
  stripe_enabled: boolean;
  stripe_public_key: string;
  stripe_secret_key: string;
  stripe_webhook_secret: string;
  paypal_enabled: boolean;
  paypal_client_id: string;
  paypal_client_secret: string;
  paypal_sandbox: boolean;
  bank_transfer_enabled: boolean;
  bank_details: string;
  cash_payment_enabled: boolean;
  check_payment_enabled: boolean;
  payment_processing_fee: number;
  refund_policy: string;
  terms_of_service: string;
}

// Email Settings Interface
export interface EmailSettings {
  mail_driver: string;
  mail_host: string;
  mail_port: number;
  mail_username: string;
  mail_password: string;
  mail_encryption: string;
  mail_from_address: string;
  mail_from_name: string;
  welcome_email_enabled: boolean;
  bid_confirmation_enabled: boolean;
  auction_reminder_enabled: boolean;
  auction_end_notification: boolean;
  payment_reminder_enabled: boolean;
  newsletter_enabled: boolean;
}

// System Settings Interface
export interface SystemSettings {
  debug_mode: boolean;
  log_level: string;
  cache_enabled: boolean;
  cache_driver: string;
  session_lifetime: number;
  max_upload_size: number;
  allowed_file_types: string[];
  image_quality: number;
  thumbnail_size: number;
  backup_enabled: boolean;
  backup_frequency: string;
  backup_retention_days: number;
  api_rate_limit: number;
  security_headers: boolean;
  two_factor_auth: boolean;
  password_requirements: {
    min_length: number;
    require_uppercase: boolean;
    require_lowercase: boolean;
    require_numbers: boolean;
    require_symbols: boolean;
  };
}

// Settings Group Type
export type SettingsGroup = 'general' | 'auction' | 'payment' | 'email' | 'system';

export const useAdminSettings = defineStore('adminSettings', () => {
  // State
  const generalSettings = ref<GeneralSettings>({
    site_name: '',
    site_description: '',
    site_url: '',
    admin_email: '',
    support_email: '',
    phone: '',
    address: '',
    timezone: 'UTC',
    date_format: 'Y-m-d',
    time_format: 'H:i:s',
    currency: 'USD',
    currency_symbol: '$',
    language: 'en',
    maintenance_mode: false,
    maintenance_message: ''
  });

  const auctionSettings = ref<AuctionSettings>({
    default_auction_duration: 7,
    min_bid_increment: 1,
    max_bid_increment: 1000,
    auto_extend_time: 5,
    auto_extend_trigger: 2,
    allow_proxy_bidding: true,
    require_registration: false,
    registration_approval: false,
    bid_deposit_required: false,
    bid_deposit_percentage: 10,
    commission_rate: 10,
    buyer_premium: 0,
    seller_commission: 0,
    payment_deadline_days: 7,
    pickup_deadline_days: 14,
    late_payment_fee: 0,
    default_reserve_price: 0,
    allow_reserve_price: true,
    show_reserve_met: true,
    show_bidder_names: false,
    show_bid_history: true,
    email_notifications: true,
    sms_notifications: false
  });

  const paymentSettings = ref<PaymentSettings>({
    stripe_enabled: false,
    stripe_public_key: '',
    stripe_secret_key: '',
    stripe_webhook_secret: '',
    paypal_enabled: false,
    paypal_client_id: '',
    paypal_client_secret: '',
    paypal_sandbox: true,
    bank_transfer_enabled: false,
    bank_details: '',
    cash_payment_enabled: true,
    check_payment_enabled: false,
    payment_processing_fee: 0,
    refund_policy: '',
    terms_of_service: ''
  });

  const emailSettings = ref<EmailSettings>({
    mail_driver: 'smtp',
    mail_host: '',
    mail_port: 587,
    mail_username: '',
    mail_password: '',
    mail_encryption: 'tls',
    mail_from_address: '',
    mail_from_name: '',
    welcome_email_enabled: true,
    bid_confirmation_enabled: true,
    auction_reminder_enabled: true,
    auction_end_notification: true,
    payment_reminder_enabled: true,
    newsletter_enabled: false
  });

  const systemSettings = ref<SystemSettings>({
    debug_mode: false,
    log_level: 'error',
    cache_enabled: true,
    cache_driver: 'file',
    session_lifetime: 120,
    max_upload_size: 10,
    allowed_file_types: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
    image_quality: 85,
    thumbnail_size: 300,
    backup_enabled: false,
    backup_frequency: 'daily',
    backup_retention_days: 30,
    api_rate_limit: 60,
    security_headers: true,
    two_factor_auth: false,
    password_requirements: {
      min_length: 8,
      require_uppercase: true,
      require_lowercase: true,
      require_numbers: true,
      require_symbols: false
    }
  });

  const isLoading = ref(false);
  const isSaving = ref(false);
  const error = ref<string | null>(null);
  const lastSaved = ref<Record<SettingsGroup, Date | null>>({
    general: null,
    auction: null,
    payment: null,
    email: null,
    system: null
  });

  // Getters
  const hasUnsavedChanges = computed(() => {
    // This would track if there are unsaved changes
    // Implementation would depend on how you want to track changes
    return false;
  });

  const isMaintenanceMode = computed(() => generalSettings.value.maintenance_mode);

  const supportedCurrencies = computed(() => [
    { code: 'USD', symbol: '$', name: 'US Dollar' },
    { code: 'EUR', symbol: '€', name: 'Euro' },
    { code: 'GBP', symbol: '£', name: 'British Pound' },
    { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
    { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
    { code: 'JPY', symbol: '¥', name: 'Japanese Yen' }
  ]);

  const supportedTimezones = computed(() => [
    'UTC',
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Europe/Berlin',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney'
  ]);

  // Actions
  const fetchSettings = async (group: SettingsGroup) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await fetch(`/api/admin/settings/${group}`, {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch ${group} settings`);
      }

      const settings = await response.json();
      
      switch (group) {
        case 'general':
          generalSettings.value = { ...generalSettings.value, ...settings };
          break;
        case 'auction':
          auctionSettings.value = { ...auctionSettings.value, ...settings };
          break;
        case 'payment':
          paymentSettings.value = { ...paymentSettings.value, ...settings };
          break;
        case 'email':
          emailSettings.value = { ...emailSettings.value, ...settings };
          break;
        case 'system':
          systemSettings.value = { ...systemSettings.value, ...settings };
          break;
      }

      return settings;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const saveSettings = async (group: SettingsGroup, settings: any) => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch(`/api/admin/settings/${group}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to save ${group} settings`);
      }

      const savedSettings = await response.json();
      
      // Update local state
      switch (group) {
        case 'general':
          generalSettings.value = { ...generalSettings.value, ...savedSettings };
          break;
        case 'auction':
          auctionSettings.value = { ...auctionSettings.value, ...savedSettings };
          break;
        case 'payment':
          paymentSettings.value = { ...paymentSettings.value, ...savedSettings };
          break;
        case 'email':
          emailSettings.value = { ...emailSettings.value, ...savedSettings };
          break;
        case 'system':
          systemSettings.value = { ...systemSettings.value, ...savedSettings };
          break;
      }

      lastSaved.value[group] = new Date();
      
      return savedSettings;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const testEmailSettings = async () => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/admin/settings/email/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
        body: JSON.stringify(emailSettings.value),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to test email settings');
      }

      const result = await response.json();
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const testPaymentGateway = async (gateway: 'stripe' | 'paypal') => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch(`/api/admin/settings/payment/${gateway}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
        body: JSON.stringify(paymentSettings.value),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to test ${gateway} settings`);
      }

      const result = await response.json();
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const clearCache = async () => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/admin/settings/cache/clear', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to clear cache');
      }

      const result = await response.json();
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const exportSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings/export', {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to export settings');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `settings-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    }
  };

  const importSettings = async (file: File) => {
    isSaving.value = true;
    error.value = null;

    try {
      const formData = new FormData();
      formData.append('settings_file', file);

      const response = await fetch('/api/admin/settings/import', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to import settings');
      }

      const result = await response.json();
      
      // Refresh all settings
      await fetchAllSettings();
      
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const fetchAllSettings = async () => {
    await Promise.all([
      fetchSettings('general'),
      fetchSettings('auction'),
      fetchSettings('payment'),
      fetchSettings('email'),
      fetchSettings('system')
    ]);
  };

  const initialize = async () => {
    await fetchAllSettings();
  };

  const reset = () => {
    // Reset to default values
    generalSettings.value = {
      site_name: '',
      site_description: '',
      site_url: '',
      admin_email: '',
      support_email: '',
      phone: '',
      address: '',
      timezone: 'UTC',
      date_format: 'Y-m-d',
      time_format: 'H:i:s',
      currency: 'USD',
      currency_symbol: '$',
      language: 'en',
      maintenance_mode: false,
      maintenance_message: ''
    };

    // Reset other settings to defaults...
    error.value = null;
    lastSaved.value = {
      general: null,
      auction: null,
      payment: null,
      email: null,
      system: null
    };
  };

  return {
    // State
    generalSettings,
    auctionSettings,
    paymentSettings,
    emailSettings,
    systemSettings,
    isLoading,
    isSaving,
    error,
    lastSaved,

    // Getters
    hasUnsavedChanges,
    isMaintenanceMode,
    supportedCurrencies,
    supportedTimezones,

    // Actions
    fetchSettings,
    saveSettings,
    testEmailSettings,
    testPaymentGateway,
    clearCache,
    exportSettings,
    importSettings,
    fetchAllSettings,
    initialize,
    reset
  };
});

export type AdminSettingsStore = ReturnType<typeof useAdminSettings>;

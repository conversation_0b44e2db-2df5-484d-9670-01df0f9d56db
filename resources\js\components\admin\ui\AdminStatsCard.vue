<template>
  <Card :class="cardClasses">
    <div class="flex items-center justify-between">
      <div class="flex items-center flex-1">
        <div class="flex-shrink-0">
          <div :class="iconClasses">
            <component :is="iconComponent" class="w-5 h-5 text-white" />
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-500">{{ title }}</p>
          <div class="flex items-baseline space-x-2">
            <p class="text-2xl font-bold text-gray-900">
              {{ animatedValue }}
            </p>
            <span v-if="unit" class="text-sm text-gray-500">{{ unit }}</span>
          </div>

          <!-- Change Indicator -->
          <div v-if="change !== undefined" class="flex items-center space-x-2 mt-1">
            <div :class="changeClasses">
              <svg v-if="change >= 0" class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <svg v-else class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="text-sm font-medium">{{ Math.abs(change) }}%</span>
            </div>
            <span class="text-xs text-gray-500">{{ changePeriod }}</span>
          </div>

          <!-- Comparison -->
          <div v-if="comparison" class="mt-2 text-xs text-gray-500">
            vs {{ comparison.label }}:
            <span :class="comparison.value >= 0 ? 'text-green-600' : 'text-red-600'">
              {{ comparison.value >= 0 ? '+' : '' }}{{ comparison.value }}%
            </span>
          </div>
        </div>
      </div>

      <!-- Trend Chart -->
      <div v-if="showTrend && trendData" class="ml-4">
        <div class="w-16 h-8">
          <svg viewBox="0 0 64 32" class="w-full h-full">
            <polyline
              :points="trendPoints"
              fill="none"
              :stroke="trendColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>

    <!-- Progress Bar -->
    <div v-if="showProgress && progress !== undefined" class="mt-4">
      <div class="flex justify-between text-xs text-gray-500 mb-1">
        <span>Progress</span>
        <span>{{ progress }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          :class="progressBarClasses"
          :style="{ width: `${Math.min(progress, 100)}%` }"
          class="h-2 rounded-full transition-all duration-1000 ease-out"
        />
      </div>
    </div>

    <!-- Additional Stats -->
    <div v-if="additionalStats && additionalStats.length > 0" class="mt-4 pt-4 border-t border-gray-100">
      <div class="grid grid-cols-2 gap-4">
        <div v-for="stat in additionalStats" :key="stat.label" class="text-center">
          <div class="text-lg font-semibold text-gray-900">{{ stat.value }}</div>
          <div class="text-xs text-gray-500">{{ stat.label }}</div>
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import { Card } from '@/components/ui';

interface AdditionalStat {
  label: string;
  value: string | number;
}

interface Comparison {
  label: string;
  value: number;
}

interface Props {
  title: string;
  value: string | number;
  change?: number;
  changePeriod?: string;
  icon: 'auctions' | 'revenue' | 'items' | 'users' | 'orders' | 'analytics';
  color?: 'blue' | 'green' | 'yellow' | 'purple' | 'red' | 'indigo';
  unit?: string;
  animated?: boolean;
  showTrend?: boolean;
  trendData?: number[];
  showProgress?: boolean;
  progress?: number;
  comparison?: Comparison;
  additionalStats?: AdditionalStat[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  color: 'blue',
  changePeriod: 'from last month',
  animated: true,
  showTrend: false,
  showProgress: false,
  loading: false
});

// State for animation
const animatedDisplayValue = ref(0);

// Icon components
const iconComponents = {
  auctions: () => `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
    </svg>
  `,
  revenue: () => `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
    </svg>
  `,
  items: () => `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
    </svg>
  `,
  users: () => `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
    </svg>
  `,
  orders: () => `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
    </svg>
  `,
  analytics: () => `
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
    </svg>
  `
};

// Computed properties
const cardClasses = computed(() => {
  const baseClasses = 'p-6 hover:shadow-lg transition-all duration-300';
  const loadingClasses = props.loading ? 'opacity-50 pointer-events-none' : '';
  return `${baseClasses} ${loadingClasses}`;
});

const iconComponent = computed(() => {
  const iconSvg = iconComponents[props.icon]();
  return {
    template: iconSvg
  };
});

const iconClasses = computed(() => {
  const baseClasses = 'w-8 h-8 rounded-md flex items-center justify-center transition-transform duration-300 hover:scale-110';
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    purple: 'bg-purple-500',
    red: 'bg-red-500',
    indigo: 'bg-indigo-500'
  };

  return `${baseClasses} ${colorClasses[props.color]}`;
});

const numericValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value;
  }
  // Try to parse string as number
  const parsed = parseFloat(props.value.toString().replace(/[^0-9.-]/g, ''));
  return isNaN(parsed) ? 0 : parsed;
});

const animatedValue = computed(() => {
  if (!props.animated || typeof props.value !== 'number') {
    return displayValue.value;
  }
  return formatNumber(animatedDisplayValue.value);
});

const displayValue = computed(() => {
  if (typeof props.value === 'number') {
    return formatNumber(props.value);
  }
  return props.value;
});

const changeClasses = computed(() => {
  if (props.change === undefined) return '';
  const baseClasses = 'flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium';
  const colorClass = props.change >= 0
    ? 'bg-green-100 text-green-800'
    : 'bg-red-100 text-red-800';
  return `${baseClasses} ${colorClass}`;
});

const trendColor = computed(() => {
  if (!props.trendData || props.trendData.length === 0) return '#6b7280';
  const first = props.trendData[0];
  const last = props.trendData[props.trendData.length - 1];
  return last >= first ? '#10b981' : '#ef4444';
});

const trendPoints = computed(() => {
  if (!props.trendData || props.trendData.length === 0) return '';

  const width = 64;
  const height = 32;
  const padding = 2;

  const min = Math.min(...props.trendData);
  const max = Math.max(...props.trendData);
  const range = max - min || 1;

  return props.trendData
    .map((value, index) => {
      const x = padding + (index / (props.trendData!.length - 1)) * (width - 2 * padding);
      const y = height - padding - ((value - min) / range) * (height - 2 * padding);
      return `${x},${y}`;
    })
    .join(' ');
});

const progressBarClasses = computed(() => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    purple: 'bg-purple-500',
    red: 'bg-red-500',
    indigo: 'bg-indigo-500'
  };

  return colorClasses[props.color];
});

// Helper functions
const formatNumber = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M';
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K';
  }
  return value.toLocaleString();
};

const animateValue = (start: number, end: number, duration: number = 1000) => {
  const startTime = Date.now();
  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Easing function (ease-out)
    const easeOut = 1 - Math.pow(1 - progress, 3);

    animatedDisplayValue.value = start + (end - start) * easeOut;

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  requestAnimationFrame(animate);
};

// Watch for value changes to trigger animation
watch(() => props.value, (newValue, oldValue) => {
  if (props.animated && typeof newValue === 'number') {
    const oldNum = typeof oldValue === 'number' ? oldValue : 0;
    animateValue(oldNum, newValue);
  }
}, { immediate: false });

// Initialize animation on mount
onMounted(() => {
  if (props.animated && typeof props.value === 'number') {
    animateValue(0, props.value);
  } else {
    animatedDisplayValue.value = numericValue.value;
  }
});
</script>

<style scoped>
/* Enhanced AdminStatsCard styles */
.admin-stats-card {
  @apply transition-all duration-300 ease-in-out;
}

.admin-stats-card:hover {
  @apply transform -translate-y-1;
}

/* Icon hover animation */
.icon-container {
  @apply transition-transform duration-300 ease-in-out;
}

.icon-container:hover {
  @apply scale-110;
}

/* Progress bar animation */
.progress-bar {
  @apply transition-all duration-1000 ease-out;
}

/* Trend line animation */
.trend-line {
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  animation: drawLine 1s ease-out forwards;
}

@keyframes drawLine {
  to {
    stroke-dashoffset: 0;
  }
}

/* Loading state */
.loading {
  @apply animate-pulse;
}

/* Change indicator pulse */
.change-indicator {
  @apply transition-all duration-300;
}

.change-indicator.positive {
  @apply animate-pulse;
}

/* Number counter animation */
.animated-number {
  @apply transition-all duration-300;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .admin-stats-card {
    @apply p-4;
  }

  .trend-chart {
    @apply hidden;
  }
}
</style>

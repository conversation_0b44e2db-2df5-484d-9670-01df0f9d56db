<template>
  <AdminListTemplate
    title="Orders"
    subtitle="Manage customer orders and transactions"
    :loading="loading"
    :error="error"
    :items="orders"
    :columns="columns"
    :show-create-button="true"
    create-button-text="New Order"
    create-route="/admin-spa/sales/orders/create"
    empty-state-title="No orders found"
    empty-state-message="Orders will appear here when customers make purchases."
    :show-pagination="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
  >
    <template #actions>
      <div class="flex space-x-3">
        <Button variant="outline" size="sm" @click="exportOrders">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"/>
          </svg>
          Export
        </Button>
        <Button variant="primary" size="sm" @click="handleCreate">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          New Order
        </Button>
      </div>
    </template>

    <template #filters>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Select
          v-model="filters.status"
          placeholder="Filter by status"
          :options="statusOptions"
          @change="applyFilters"
        />
        <Select
          v-model="filters.dateRange"
          placeholder="Date range"
          :options="dateRangeOptions"
          @change="applyFilters"
        />
        <Input
          v-model="filters.customer"
          placeholder="Search customer..."
          @input="applyFilters"
        />
        <Input
          v-model="filters.orderId"
          placeholder="Order ID..."
          @input="applyFilters"
        />
      </div>
    </template>

    <template #item-actions="{ item }">
      <div class="flex space-x-2">
        <Button variant="ghost" size="sm" @click="viewOrder(item)">
          View
        </Button>
        <Button variant="ghost" size="sm" @click="editOrder(item)">
          Edit
        </Button>
        <Button 
          v-if="item.status === 'pending'" 
          variant="ghost" 
          size="sm" 
          @click="processOrder(item)"
        >
          Process
        </Button>
      </div>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Select, Input } from '@/components/ui';

// Router
const router = useRouter();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const currentPage = ref(1);
const totalPages = ref(5);
const totalItems = ref(98);

// Filters
const filters = ref({
  status: '',
  dateRange: '',
  customer: '',
  orderId: ''
});

// Options
const statusOptions = [
  { value: '', label: 'All Statuses' },
  { value: 'pending', label: 'Pending' },
  { value: 'processing', label: 'Processing' },
  { value: 'completed', label: 'Completed' },
  { value: 'cancelled', label: 'Cancelled' }
];

const dateRangeOptions = [
  { value: '', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' }
];

// Table columns
const columns = [
  { key: 'id', label: 'Order ID', sortable: true },
  { key: 'customer', label: 'Customer', sortable: true },
  { key: 'items', label: 'Items', sortable: false },
  { key: 'amount', label: 'Amount', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'date', label: 'Date', sortable: true },
  { key: 'actions', label: 'Actions', sortable: false }
];

// Mock data
const allOrders = ref([
  {
    id: 1001,
    customer: 'John Doe',
    customerEmail: '<EMAIL>',
    items: 3,
    amount: 299.99,
    status: 'completed',
    date: new Date('2024-01-15'),
    paymentMethod: 'Credit Card'
  },
  {
    id: 1002,
    customer: 'Jane Smith',
    customerEmail: '<EMAIL>',
    items: 1,
    amount: 149.50,
    status: 'pending',
    date: new Date('2024-01-14'),
    paymentMethod: 'PayPal'
  },
  {
    id: 1003,
    customer: 'Bob Johnson',
    customerEmail: '<EMAIL>',
    items: 2,
    amount: 399.99,
    status: 'processing',
    date: new Date('2024-01-13'),
    paymentMethod: 'Credit Card'
  },
  {
    id: 1004,
    customer: 'Alice Brown',
    customerEmail: '<EMAIL>',
    items: 1,
    amount: 199.99,
    status: 'completed',
    date: new Date('2024-01-12'),
    paymentMethod: 'Bank Transfer'
  },
  {
    id: 1005,
    customer: 'Charlie Wilson',
    customerEmail: '<EMAIL>',
    items: 1,
    amount: 89.99,
    status: 'cancelled',
    date: new Date('2024-01-11'),
    paymentMethod: 'Credit Card'
  }
]);

// Computed
const orders = computed(() => {
  let filtered = allOrders.value;

  // Apply filters
  if (filters.value.status) {
    filtered = filtered.filter(order => order.status === filters.value.status);
  }
  if (filters.value.customer) {
    filtered = filtered.filter(order => 
      order.customer.toLowerCase().includes(filters.value.customer.toLowerCase()) ||
      order.customerEmail.toLowerCase().includes(filters.value.customer.toLowerCase())
    );
  }
  if (filters.value.orderId) {
    filtered = filtered.filter(order => 
      order.id.toString().includes(filters.value.orderId)
    );
  }

  return filtered;
});

// Methods
const handleCreate = () => {
  router.push('/admin-spa/sales/orders/create');
};

const handleSearch = (query: string) => {
  filters.value.customer = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  console.log('Sorting by:', column, order);
  // Implement sorting logic
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  // Load data for new page
};

const applyFilters = () => {
  currentPage.value = 1;
  // Apply filters and reload data
};

const exportOrders = () => {
  console.log('Exporting orders...');
};

const viewOrder = (order: any) => {
  router.push(`/admin-spa/sales/orders/${order.id}`);
};

const editOrder = (order: any) => {
  router.push(`/admin-spa/sales/orders/${order.id}/edit`);
};

const processOrder = (order: any) => {
  console.log('Processing order:', order.id);
  // Implement order processing logic
};

// Lifecycle
onMounted(() => {
  // Load orders data
});
</script>

<template>
  <div class="admin-form">
    <!-- Form Header -->
    <div v-if="title || subtitle" class="mb-6">
      <h3 v-if="title" class="text-lg font-medium text-gray-900">{{ title }}</h3>
      <p v-if="subtitle" class="mt-1 text-sm text-gray-500">{{ subtitle }}</p>
    </div>

    <!-- Form Validation Errors -->
    <Alert v-if="hasValidationErrors" variant="error" class="mb-6">
      <div class="space-y-1">
        <p class="font-medium">Please correct the following errors:</p>
        <ul class="list-disc list-inside space-y-1">
          <li v-for="error in validationErrors" :key="error" class="text-sm">{{ error }}</li>
        </ul>
      </div>
    </Alert>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Form Sections -->
      <div v-for="section in sections" :key="section.key" class="space-y-6">
        <!-- Section Header -->
        <div v-if="section.title || section.description" class="border-b border-gray-200 pb-4">
          <h4 v-if="section.title" class="text-base font-medium text-gray-900">{{ section.title }}</h4>
          <p v-if="section.description" class="mt-1 text-sm text-gray-500">{{ section.description }}</p>
        </div>

        <!-- Section Fields -->
        <div :class="getSectionClasses(section)">
          <slot :name="`section-${section.key}`" :section="section">
            <!-- Default section content -->
            <div class="text-sm text-gray-500">
              Section content for "{{ section.title || section.key }}" should be provided via slot.
            </div>
          </slot>
        </div>
      </div>

      <!-- Default Form Content (when no sections) -->
      <div v-if="sections.length === 0" class="space-y-6">
        <slot name="default">
          <!-- Default form fields -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <AdminFormField
              label="Name"
              name="name"
              type="text"
              placeholder="Enter name"
              required
            />
            <AdminFormField
              label="Email"
              name="email"
              type="email"
              placeholder="Enter email"
              required
            />
          </div>
        </slot>
      </div>

      <!-- File Upload Section -->
      <div v-if="showFileUpload" class="space-y-4">
        <h4 class="text-base font-medium text-gray-900">File Attachments</h4>
        <AdminFileUpload
          :multiple="allowMultipleFiles"
          :accept="acceptedFileTypes"
          :max-size="maxFileSize"
          @upload="handleFileUpload"
          @remove="handleFileRemove"
        />
      </div>

      <!-- Rich Text Editor Section -->
      <div v-if="showRichText" class="space-y-4">
        <h4 class="text-base font-medium text-gray-900">Description</h4>
        <AdminRichTextEditor
          v-model="richTextContent"
          :placeholder="richTextPlaceholder"
          @change="handleRichTextChange"
        />
      </div>

      <!-- Form Actions -->
      <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center pt-6 border-t border-gray-200">
        <!-- Left Actions -->
        <div class="flex gap-3 mb-4 sm:mb-0">
          <slot name="left-actions">
            <Button
              v-if="showDraft"
              variant="outline"
              type="button"
              @click="handleSaveDraft"
              :loading="savingDraft"
              :disabled="saving"
            >
              Save Draft
            </Button>
          </slot>
        </div>

        <!-- Right Actions -->
        <div class="flex gap-3">
          <slot name="actions">
            <Button
              variant="outline"
              type="button"
              @click="handleCancel"
              :disabled="saving || savingDraft"
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              type="submit"
              :loading="saving"
              :disabled="!isValid || savingDraft"
            >
              {{ submitButtonText }}
            </Button>
          </slot>
        </div>
      </div>

      <!-- Last Saved Indicator -->
      <div v-if="showLastSaved && lastSaved" class="text-sm text-gray-500 text-center">
        Last saved: {{ formatLastSaved(lastSaved) }}
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Button, Alert } from '@/components/ui';
import AdminFormField from './AdminFormField.vue';
import AdminFileUpload from './AdminFileUpload.vue';
import AdminRichTextEditor from './AdminRichTextEditor.vue';

// Types
interface FormSection {
  key: string;
  title?: string;
  description?: string;
  layout?: 'stack' | 'grid' | 'columns';
  columns?: number;
}

interface Props {
  title?: string;
  subtitle?: string;
  sections?: FormSection[];
  loading?: boolean;
  saving?: boolean;
  savingDraft?: boolean;
  isValid?: boolean;
  validationErrors?: string[];
  submitButtonText?: string;
  showDraft?: boolean;
  showLastSaved?: boolean;
  lastSaved?: Date | null;
  showFileUpload?: boolean;
  allowMultipleFiles?: boolean;
  acceptedFileTypes?: string;
  maxFileSize?: number;
  showRichText?: boolean;
  richTextPlaceholder?: string;
  modelValue?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  sections: () => [],
  loading: false,
  saving: false,
  savingDraft: false,
  isValid: true,
  validationErrors: () => [],
  submitButtonText: 'Save',
  showDraft: false,
  showLastSaved: false,
  lastSaved: null,
  showFileUpload: false,
  allowMultipleFiles: false,
  acceptedFileTypes: '*',
  maxFileSize: 10, // MB
  showRichText: false,
  richTextPlaceholder: 'Enter description...',
  modelValue: () => ({})
});

const emit = defineEmits<{
  submit: [data: Record<string, any>];
  'save-draft': [data: Record<string, any>];
  cancel: [];
  'file-upload': [files: File[]];
  'file-remove': [file: File];
  'rich-text-change': [content: string];
  'update:modelValue': [value: Record<string, any>];
}>();

// State
const richTextContent = ref('');
const formData = ref<Record<string, any>>({ ...props.modelValue });

// Computed
const hasValidationErrors = computed(() => {
  return props.validationErrors.length > 0;
});

const getSectionClasses = (section: FormSection): string => {
  const baseClasses = 'space-y-4';
  
  switch (section.layout) {
    case 'grid':
      const columns = section.columns || 2;
      return `${baseClasses} grid grid-cols-1 md:grid-cols-${columns} gap-6`;
    case 'columns':
      return `${baseClasses} grid grid-cols-1 lg:grid-cols-2 gap-6`;
    case 'stack':
    default:
      return baseClasses;
  }
};

// Methods
const handleSubmit = () => {
  if (props.isValid) {
    emit('submit', formData.value);
  }
};

const handleSaveDraft = () => {
  emit('save-draft', formData.value);
};

const handleCancel = () => {
  emit('cancel');
};

const handleFileUpload = (files: File[]) => {
  emit('file-upload', files);
};

const handleFileRemove = (file: File) => {
  emit('file-remove', file);
};

const handleRichTextChange = (content: string) => {
  richTextContent.value = content;
  emit('rich-text-change', content);
};

const formatLastSaved = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  
  if (minutes < 1) {
    return 'Just now';
  } else if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleString();
  }
};

// Watch for model value changes
watch(() => props.modelValue, (newValue) => {
  formData.value = { ...newValue };
}, { deep: true });

watch(formData, (newValue) => {
  emit('update:modelValue', newValue);
}, { deep: true });
</script>

<style scoped>
/* Custom styles for AdminForm */
.admin-form {
  @apply w-full max-w-none;
}

/* Form section spacing */
.admin-form .form-section {
  @apply space-y-4;
}

/* Grid layout responsive adjustments */
@media (max-width: 768px) {
  .admin-form .grid {
    @apply grid-cols-1;
  }
}

/* Form field focus styles */
.admin-form :deep(.form-field:focus-within) {
  @apply ring-2 ring-blue-500 ring-opacity-20;
}

/* File upload area */
.admin-form .file-upload-area {
  @apply border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors;
}

/* Rich text editor container */
.admin-form .rich-text-container {
  @apply border border-gray-300 rounded-lg overflow-hidden;
}
</style>

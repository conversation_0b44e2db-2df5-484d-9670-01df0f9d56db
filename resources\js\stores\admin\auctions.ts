import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Auction, AuctionType, Item } from '@/types';

// Extended Auction interface for admin operations
export interface AdminAuction extends Auction {
  total_bids?: number;
  total_bidders?: number;
  highest_bid?: number;
  reserve_met?: boolean;
  commission_rate?: number;
  total_commission?: number;
  payment_status?: 'pending' | 'partial' | 'paid' | 'refunded';
  shipping_status?: 'pending' | 'processing' | 'shipped' | 'delivered';
  notes?: string;
  featured?: boolean;
  promoted?: boolean;
  analytics?: {
    views: number;
    watchers: number;
    bid_activity: Array<{
      timestamp: string;
      bid_amount: number;
      bidder_id: number;
    }>;
  };
}

// Auction Filter Interface
export interface AuctionFilter {
  search?: string;
  status?: string;
  type?: string;
  dateFrom?: string;
  dateTo?: string;
  minBid?: number;
  maxBid?: number;
  featured?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  perPage?: number;
}

// Auction Stats Interface
export interface AuctionStats {
  total: number;
  active: number;
  completed: number;
  draft: number;
  cancelled: number;
  totalRevenue: number;
  totalBids: number;
  averageBidsPerAuction: number;
  conversionRate: number;
  topPerformingCategory?: string;
}

// Live Auction Monitoring Interface
export interface LiveAuctionData {
  id: number;
  title: string;
  current_bid: number;
  bid_count: number;
  time_remaining: number;
  active_bidders: number;
  last_bid_time: string;
  status: 'active' | 'ending_soon' | 'ended';
}

export const useAdminAuctions = defineStore('adminAuctions', () => {
  // State
  const auctions = ref<AdminAuction[]>([]);
  const currentAuction = ref<AdminAuction | null>(null);
  const auctionTypes = ref<AuctionType[]>([]);
  const liveAuctions = ref<LiveAuctionData[]>([]);
  const auctionStats = ref<AuctionStats>({
    total: 0,
    active: 0,
    completed: 0,
    draft: 0,
    cancelled: 0,
    totalRevenue: 0,
    totalBids: 0,
    averageBidsPerAuction: 0,
    conversionRate: 0
  });

  const isLoading = ref(false);
  const isSaving = ref(false);
  const error = ref<string | null>(null);
  const selectedAuctions = ref<number[]>([]);
  
  // Pagination
  const currentPage = ref(1);
  const totalPages = ref(1);
  const totalItems = ref(0);
  const perPage = ref(25);
  
  // Filters
  const filters = ref<AuctionFilter>({
    search: '',
    status: '',
    type: '',
    sortBy: 'created_at',
    sortOrder: 'desc',
    page: 1,
    perPage: 25
  });

  // Live monitoring
  const liveMonitoringEnabled = ref(false);
  const liveUpdateInterval = ref<number | null>(null);

  // Getters
  const activeAuctions = computed(() => 
    auctions.value.filter(auction => auction.status === 'active')
  );

  const draftAuctions = computed(() => 
    auctions.value.filter(auction => auction.status === 'draft')
  );

  const completedAuctions = computed(() => 
    auctions.value.filter(auction => auction.status === 'completed')
  );

  const endingSoonAuctions = computed(() => 
    liveAuctions.value.filter(auction => auction.status === 'ending_soon')
  );

  const hasSelectedAuctions = computed(() => selectedAuctions.value.length > 0);

  const totalRevenueCurrency = computed(() => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(auctionStats.value.totalRevenue);
  });

  // Actions
  const fetchAuctions = async (params?: Partial<AuctionFilter>) => {
    isLoading.value = true;
    error.value = null;

    try {
      const queryParams = new URLSearchParams({
        ...filters.value,
        ...params,
        page: String(params?.page || filters.value.page || 1),
        perPage: String(params?.perPage || filters.value.perPage || 25)
      }).toString();

      const response = await fetch(`/api/admin/auctions?${queryParams}`, {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch auctions');
      }

      const data = await response.json();
      
      auctions.value = data.data || [];
      currentPage.value = data.current_page || 1;
      totalPages.value = data.last_page || 1;
      totalItems.value = data.total || 0;
      perPage.value = data.per_page || 25;

      // Update filters with actual values
      if (params) {
        Object.assign(filters.value, params);
      }

      return data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchAuction = async (auctionId: number) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await fetch(`/api/admin/auctions/${auctionId}`, {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch auction');
      }

      const auction = await response.json();
      currentAuction.value = auction;
      
      return auction;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createAuction = async (auctionData: Partial<AdminAuction>) => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/admin/auctions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
        body: JSON.stringify(auctionData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create auction');
      }

      const auction = await response.json();
      auctions.value.unshift(auction);
      
      return auction;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const updateAuction = async (auctionId: number, auctionData: Partial<AdminAuction>) => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch(`/api/admin/auctions/${auctionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
        body: JSON.stringify(auctionData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update auction');
      }

      const updatedAuction = await response.json();
      
      // Update in auctions array
      const index = auctions.value.findIndex(a => a.id === auctionId);
      if (index !== -1) {
        auctions.value[index] = updatedAuction;
      }
      
      // Update current auction if it's the same
      if (currentAuction.value?.id === auctionId) {
        currentAuction.value = updatedAuction;
      }
      
      return updatedAuction;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const deleteAuction = async (auctionId: number) => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch(`/api/admin/auctions/${auctionId}`, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete auction');
      }

      // Remove from auctions array
      auctions.value = auctions.value.filter(a => a.id !== auctionId);
      
      // Clear current auction if it's the same
      if (currentAuction.value?.id === auctionId) {
        currentAuction.value = null;
      }
      
      // Remove from selected auctions
      selectedAuctions.value = selectedAuctions.value.filter(id => id !== auctionId);
      
      return true;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const publishAuction = async (auctionId: number) => {
    return await updateAuction(auctionId, { status: 'active' });
  };

  const endAuction = async (auctionId: number) => {
    return await updateAuction(auctionId, { status: 'completed' });
  };

  const cancelAuction = async (auctionId: number) => {
    return await updateAuction(auctionId, { status: 'cancelled' });
  };

  const fetchAuctionTypes = async () => {
    try {
      const response = await fetch('/api/admin/auction-types', {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        auctionTypes.value = data;
      }
    } catch (err) {
      console.error('Failed to fetch auction types:', err);
    }
  };

  const fetchAuctionStats = async () => {
    try {
      const response = await fetch('/api/admin/auctions/stats', {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const stats = await response.json();
        auctionStats.value = stats;
      }
    } catch (err) {
      console.error('Failed to fetch auction stats:', err);
    }
  };

  const fetchLiveAuctions = async () => {
    try {
      const response = await fetch('/api/admin/auctions/live', {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        liveAuctions.value = data;
      }
    } catch (err) {
      console.error('Failed to fetch live auctions:', err);
    }
  };

  // Live monitoring
  const startLiveMonitoring = () => {
    if (liveUpdateInterval.value) return;
    
    liveMonitoringEnabled.value = true;
    liveUpdateInterval.value = window.setInterval(() => {
      fetchLiveAuctions();
    }, 5000); // Update every 5 seconds
  };

  const stopLiveMonitoring = () => {
    if (liveUpdateInterval.value) {
      clearInterval(liveUpdateInterval.value);
      liveUpdateInterval.value = null;
    }
    liveMonitoringEnabled.value = false;
  };

  // Selection management
  const selectAuction = (auctionId: number) => {
    if (!selectedAuctions.value.includes(auctionId)) {
      selectedAuctions.value.push(auctionId);
    }
  };

  const deselectAuction = (auctionId: number) => {
    selectedAuctions.value = selectedAuctions.value.filter(id => id !== auctionId);
  };

  const toggleAuctionSelection = (auctionId: number) => {
    if (selectedAuctions.value.includes(auctionId)) {
      deselectAuction(auctionId);
    } else {
      selectAuction(auctionId);
    }
  };

  const selectAllAuctions = () => {
    selectedAuctions.value = auctions.value.map(auction => auction.id);
  };

  const clearSelection = () => {
    selectedAuctions.value = [];
  };

  // Filter management
  const updateFilters = (newFilters: Partial<AuctionFilter>) => {
    Object.assign(filters.value, newFilters);
    filters.value.page = 1; // Reset to first page when filters change
  };

  const clearFilters = () => {
    filters.value = {
      search: '',
      status: '',
      type: '',
      sortBy: 'created_at',
      sortOrder: 'desc',
      page: 1,
      perPage: 25
    };
  };

  const initialize = async () => {
    await Promise.all([
      fetchAuctions(),
      fetchAuctionTypes(),
      fetchAuctionStats(),
      fetchLiveAuctions()
    ]);
  };

  const reset = () => {
    auctions.value = [];
    currentAuction.value = null;
    auctionTypes.value = [];
    liveAuctions.value = [];
    auctionStats.value = {
      total: 0,
      active: 0,
      completed: 0,
      draft: 0,
      cancelled: 0,
      totalRevenue: 0,
      totalBids: 0,
      averageBidsPerAuction: 0,
      conversionRate: 0
    };
    selectedAuctions.value = [];
    error.value = null;
    clearFilters();
    stopLiveMonitoring();
  };

  return {
    // State
    auctions,
    currentAuction,
    auctionTypes,
    liveAuctions,
    auctionStats,
    isLoading,
    isSaving,
    error,
    selectedAuctions,
    currentPage,
    totalPages,
    totalItems,
    perPage,
    filters,
    liveMonitoringEnabled,

    // Getters
    activeAuctions,
    draftAuctions,
    completedAuctions,
    endingSoonAuctions,
    hasSelectedAuctions,
    totalRevenueCurrency,

    // Actions
    fetchAuctions,
    fetchAuction,
    createAuction,
    updateAuction,
    deleteAuction,
    publishAuction,
    endAuction,
    cancelAuction,
    fetchAuctionTypes,
    fetchAuctionStats,
    fetchLiveAuctions,
    startLiveMonitoring,
    stopLiveMonitoring,
    selectAuction,
    deselectAuction,
    toggleAuctionSelection,
    selectAllAuctions,
    clearSelection,
    updateFilters,
    clearFilters,
    initialize,
    reset
  };
});

export type AdminAuctionsStore = ReturnType<typeof useAdminAuctions>;

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { User, Role } from '@/types';

// Extended User interface for admin operations
export interface AdminUser extends User {
  status: 'active' | 'inactive' | 'suspended' | 'pending';
  last_login_at?: string;
  email_verified_at?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  avatar?: string;
  bio?: string;
  preferences?: Record<string, any>;
  permissions?: string[];
  total_bids?: number;
  total_spent?: number;
  total_won?: number;
  registration_source?: string;
  notes?: string;
}

// User Filter Interface
export interface UserFilter {
  search?: string;
  status?: string;
  role?: string;
  verified?: boolean;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  perPage?: number;
}

// User Stats Interface
export interface UserStats {
  total: number;
  active: number;
  inactive: number;
  suspended: number;
  pending: number;
  verified: number;
  unverified: number;
  newThisMonth: number;
  newThisWeek: number;
}

// Bulk Action Interface
export interface BulkAction {
  action: 'activate' | 'deactivate' | 'suspend' | 'delete' | 'verify' | 'assign_role' | 'remove_role';
  userIds: number[];
  data?: Record<string, any>;
}

export const useAdminUsers = defineStore('adminUsers', () => {
  // State
  const users = ref<AdminUser[]>([]);
  const currentUser = ref<AdminUser | null>(null);
  const roles = ref<Role[]>([]);
  const permissions = ref<string[]>([]);
  const userStats = ref<UserStats>({
    total: 0,
    active: 0,
    inactive: 0,
    suspended: 0,
    pending: 0,
    verified: 0,
    unverified: 0,
    newThisMonth: 0,
    newThisWeek: 0
  });

  const isLoading = ref(false);
  const isSaving = ref(false);
  const error = ref<string | null>(null);
  const selectedUsers = ref<number[]>([]);
  
  // Pagination
  const currentPage = ref(1);
  const totalPages = ref(1);
  const totalItems = ref(0);
  const perPage = ref(25);
  
  // Filters
  const filters = ref<UserFilter>({
    search: '',
    status: '',
    role: '',
    verified: undefined,
    sortBy: 'created_at',
    sortOrder: 'desc',
    page: 1,
    perPage: 25
  });

  // Getters
  const activeUsers = computed(() => 
    users.value.filter(user => user.status === 'active')
  );

  const suspendedUsers = computed(() => 
    users.value.filter(user => user.status === 'suspended')
  );

  const pendingUsers = computed(() => 
    users.value.filter(user => user.status === 'pending')
  );

  const hasSelectedUsers = computed(() => selectedUsers.value.length > 0);

  const filteredUsers = computed(() => {
    let filtered = [...users.value];

    if (filters.value.search) {
      const search = filters.value.search.toLowerCase();
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(search) ||
        user.email.toLowerCase().includes(search)
      );
    }

    if (filters.value.status) {
      filtered = filtered.filter(user => user.status === filters.value.status);
    }

    if (filters.value.role) {
      filtered = filtered.filter(user => 
        user.roles?.some(role => role.name === filters.value.role)
      );
    }

    if (filters.value.verified !== undefined) {
      filtered = filtered.filter(user => 
        !!user.email_verified_at === filters.value.verified
      );
    }

    return filtered;
  });

  // Actions
  const fetchUsers = async (params?: Partial<UserFilter>) => {
    isLoading.value = true;
    error.value = null;

    try {
      const queryParams = new URLSearchParams({
        ...filters.value,
        ...params,
        page: String(params?.page || filters.value.page || 1),
        perPage: String(params?.perPage || filters.value.perPage || 25)
      }).toString();

      const response = await fetch(`/api/admin/users?${queryParams}`, {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      
      users.value = data.data || [];
      currentPage.value = data.current_page || 1;
      totalPages.value = data.last_page || 1;
      totalItems.value = data.total || 0;
      perPage.value = data.per_page || 25;

      // Update filters with actual values
      if (params) {
        Object.assign(filters.value, params);
      }

      return data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchUser = async (userId: number) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user');
      }

      const user = await response.json();
      currentUser.value = user;
      
      return user;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createUser = async (userData: Partial<AdminUser>) => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create user');
      }

      const user = await response.json();
      users.value.unshift(user);
      
      return user;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const updateUser = async (userId: number, userData: Partial<AdminUser>) => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update user');
      }

      const updatedUser = await response.json();
      
      // Update in users array
      const index = users.value.findIndex(u => u.id === userId);
      if (index !== -1) {
        users.value[index] = updatedUser;
      }
      
      // Update current user if it's the same
      if (currentUser.value?.id === userId) {
        currentUser.value = updatedUser;
      }
      
      return updatedUser;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const deleteUser = async (userId: number) => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete user');
      }

      // Remove from users array
      users.value = users.value.filter(u => u.id !== userId);
      
      // Clear current user if it's the same
      if (currentUser.value?.id === userId) {
        currentUser.value = null;
      }
      
      // Remove from selected users
      selectedUsers.value = selectedUsers.value.filter(id => id !== userId);
      
      return true;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const bulkAction = async (action: BulkAction) => {
    isSaving.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/admin/users/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
        body: JSON.stringify(action),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to perform bulk action');
      }

      const result = await response.json();
      
      // Refresh users list
      await fetchUsers();
      
      // Clear selection
      selectedUsers.value = [];
      
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      isSaving.value = false;
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/admin/roles', {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        roles.value = data;
      }
    } catch (err) {
      console.error('Failed to fetch roles:', err);
    }
  };

  const fetchPermissions = async () => {
    try {
      const response = await fetch('/api/admin/permissions', {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        permissions.value = data;
      }
    } catch (err) {
      console.error('Failed to fetch permissions:', err);
    }
  };

  const fetchUserStats = async () => {
    try {
      const response = await fetch('/api/admin/users/stats', {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const stats = await response.json();
        userStats.value = stats;
      }
    } catch (err) {
      console.error('Failed to fetch user stats:', err);
    }
  };

  // Selection management
  const selectUser = (userId: number) => {
    if (!selectedUsers.value.includes(userId)) {
      selectedUsers.value.push(userId);
    }
  };

  const deselectUser = (userId: number) => {
    selectedUsers.value = selectedUsers.value.filter(id => id !== userId);
  };

  const toggleUserSelection = (userId: number) => {
    if (selectedUsers.value.includes(userId)) {
      deselectUser(userId);
    } else {
      selectUser(userId);
    }
  };

  const selectAllUsers = () => {
    selectedUsers.value = users.value.map(user => user.id);
  };

  const clearSelection = () => {
    selectedUsers.value = [];
  };

  // Filter management
  const updateFilters = (newFilters: Partial<UserFilter>) => {
    Object.assign(filters.value, newFilters);
    filters.value.page = 1; // Reset to first page when filters change
  };

  const clearFilters = () => {
    filters.value = {
      search: '',
      status: '',
      role: '',
      verified: undefined,
      sortBy: 'created_at',
      sortOrder: 'desc',
      page: 1,
      perPage: 25
    };
  };

  const initialize = async () => {
    await Promise.all([
      fetchUsers(),
      fetchRoles(),
      fetchPermissions(),
      fetchUserStats()
    ]);
  };

  const reset = () => {
    users.value = [];
    currentUser.value = null;
    roles.value = [];
    permissions.value = [];
    userStats.value = {
      total: 0,
      active: 0,
      inactive: 0,
      suspended: 0,
      pending: 0,
      verified: 0,
      unverified: 0,
      newThisMonth: 0,
      newThisWeek: 0
    };
    selectedUsers.value = [];
    error.value = null;
    clearFilters();
  };

  return {
    // State
    users,
    currentUser,
    roles,
    permissions,
    userStats,
    isLoading,
    isSaving,
    error,
    selectedUsers,
    currentPage,
    totalPages,
    totalItems,
    perPage,
    filters,

    // Getters
    activeUsers,
    suspendedUsers,
    pendingUsers,
    hasSelectedUsers,
    filteredUsers,

    // Actions
    fetchUsers,
    fetchUser,
    createUser,
    updateUser,
    deleteUser,
    bulkAction,
    fetchRoles,
    fetchPermissions,
    fetchUserStats,
    selectUser,
    deselectUser,
    toggleUserSelection,
    selectAllUsers,
    clearSelection,
    updateFilters,
    clearFilters,
    initialize,
    reset
  };
});

export type AdminUsersStore = ReturnType<typeof useAdminUsers>;

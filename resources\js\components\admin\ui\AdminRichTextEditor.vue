<template>
  <div class="admin-rich-text-editor">
    <!-- Toolbar -->
    <div class="border border-gray-300 border-b-0 rounded-t-lg bg-gray-50 p-2 flex flex-wrap gap-1">
      <!-- Text Formatting -->
      <div class="flex gap-1 border-r border-gray-300 pr-2 mr-2">
        <button
          type="button"
          @click="execCommand('bold')"
          :class="[
            'p-2 rounded hover:bg-gray-200 transition-colors',
            isActive('bold') ? 'bg-gray-300' : ''
          ]"
          title="Bold"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h6a4 4 0 014 4v.5a3.5 3.5 0 01-2.5 3.36A4 4 0 0115 15v.5a4 4 0 01-4 4H4a1 1 0 01-1-1V4zm2 1v5h5a2 2 0 100-4H5zm0 7v5h6a2 2 0 100-4H5z" />
          </svg>
        </button>
        
        <button
          type="button"
          @click="execCommand('italic')"
          :class="[
            'p-2 rounded hover:bg-gray-200 transition-colors',
            isActive('italic') ? 'bg-gray-300' : ''
          ]"
          title="Italic"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M8 1a1 1 0 011 1v1h2a1 1 0 110 2h-.5l-1 8H11a1 1 0 110 2H7a1 1 0 110-2h.5l1-8H7a1 1 0 110-2h2V2a1 1 0 011-1z" />
          </svg>
        </button>
        
        <button
          type="button"
          @click="execCommand('underline')"
          :class="[
            'p-2 rounded hover:bg-gray-200 transition-colors',
            isActive('underline') ? 'bg-gray-300' : ''
          ]"
          title="Underline"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 18a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM3 7a4 4 0 018 0v5a2 2 0 104 0V7a6 6 0 10-12 0v5a2 2 0 104 0V7z" />
          </svg>
        </button>
      </div>

      <!-- Lists -->
      <div class="flex gap-1 border-r border-gray-300 pr-2 mr-2">
        <button
          type="button"
          @click="execCommand('insertUnorderedList')"
          :class="[
            'p-2 rounded hover:bg-gray-200 transition-colors',
            isActive('insertUnorderedList') ? 'bg-gray-300' : ''
          ]"
          title="Bullet List"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 000 2h.01a1 1 0 100-2H3zM6 4a1 1 0 011-1h9a1 1 0 110 2H7a1 1 0 01-1-1zM3 9a1 1 0 000 2h.01a1 1 0 100-2H3zM6 9a1 1 0 011-1h9a1 1 0 110 2H7a1 1 0 01-1-1zM3 14a1 1 0 000 2h.01a1 1 0 100-2H3zM6 14a1 1 0 011-1h9a1 1 0 110 2H7a1 1 0 01-1-1z" />
          </svg>
        </button>
        
        <button
          type="button"
          @click="execCommand('insertOrderedList')"
          :class="[
            'p-2 rounded hover:bg-gray-200 transition-colors',
            isActive('insertOrderedList') ? 'bg-gray-300' : ''
          ]"
          title="Numbered List"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 3a1 1 0 000 2h.01a1 1 0 100-2H3zM6 3a1 1 0 011-1h9a1 1 0 110 2H7a1 1 0 01-1-1zM3 8a1 1 0 000 2h.01a1 1 0 100-2H3zM6 8a1 1 0 011-1h9a1 1 0 110 2H7a1 1 0 01-1-1zM3 13a1 1 0 000 2h.01a1 1 0 100-2H3zM6 13a1 1 0 011-1h9a1 1 0 110 2H7a1 1 0 01-1-1z" />
          </svg>
        </button>
      </div>

      <!-- Alignment -->
      <div class="flex gap-1 border-r border-gray-300 pr-2 mr-2">
        <button
          type="button"
          @click="execCommand('justifyLeft')"
          :class="[
            'p-2 rounded hover:bg-gray-200 transition-colors',
            isActive('justifyLeft') ? 'bg-gray-300' : ''
          ]"
          title="Align Left"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 3a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM2 7a1 1 0 011-1h8a1 1 0 110 2H3a1 1 0 01-1-1zM2 11a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM2 15a1 1 0 011-1h8a1 1 0 110 2H3a1 1 0 01-1-1z" />
          </svg>
        </button>
        
        <button
          type="button"
          @click="execCommand('justifyCenter')"
          :class="[
            'p-2 rounded hover:bg-gray-200 transition-colors',
            isActive('justifyCenter') ? 'bg-gray-300' : ''
          ]"
          title="Align Center"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 3a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zM2 11a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM5 15a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" />
          </svg>
        </button>
        
        <button
          type="button"
          @click="execCommand('justifyRight')"
          :class="[
            'p-2 rounded hover:bg-gray-200 transition-colors',
            isActive('justifyRight') ? 'bg-gray-300' : ''
          ]"
          title="Align Right"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 3a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM7 7a1 1 0 011-1h8a1 1 0 110 2H8a1 1 0 01-1-1zM2 11a1 1 0 011-1h14a1 1 0 110 2H3a1 1 0 01-1-1zM7 15a1 1 0 011-1h8a1 1 0 110 2H8a1 1 0 01-1-1z" />
          </svg>
        </button>
      </div>

      <!-- Links -->
      <div class="flex gap-1">
        <button
          type="button"
          @click="insertLink"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Insert Link"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" />
          </svg>
        </button>
        
        <button
          type="button"
          @click="execCommand('unlink')"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Remove Link"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 11-2 0V4H5v12h10v-2a1 1 0 112 0v3a1 1 0 01-1 1H4a1 1 0 01-1-1V3z" />
            <path d="M13 6a1 1 0 011-1h3a1 1 0 110 2h-3a1 1 0 01-1-1zM13 10a1 1 0 011-1h3a1 1 0 110 2h-3a1 1 0 01-1-1zM13 14a1 1 0 011-1h3a1 1 0 110 2h-3a1 1 0 01-1-1z" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Editor Content -->
    <div
      ref="editor"
      contenteditable="true"
      :class="[
        'border border-gray-300 rounded-b-lg p-4 min-h-32 max-h-96 overflow-y-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
        disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'
      ]"
      :style="{ minHeight: minHeight + 'px' }"
      @input="handleInput"
      @paste="handlePaste"
      @keydown="handleKeydown"
      @focus="handleFocus"
      @blur="handleBlur"
      v-html="content"
    />

    <!-- Character Count -->
    <div v-if="showCharCount" class="mt-2 text-right">
      <span :class="[
        'text-xs',
        characterCount > maxLength * 0.9 ? 'text-red-500' : 'text-gray-500'
      ]">
        {{ characterCount }}{{ maxLength ? `/${maxLength}` : '' }} characters
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';

interface Props {
  modelValue?: string;
  placeholder?: string;
  disabled?: boolean;
  minHeight?: number;
  maxLength?: number;
  showCharCount?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: 'Enter text...',
  disabled: false,
  minHeight: 128,
  maxLength: 0,
  showCharCount: false
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
  change: [value: string];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
}>();

// State
const editor = ref<HTMLDivElement>();
const content = ref(props.modelValue);

// Computed
const characterCount = computed(() => {
  const text = editor.value?.textContent || '';
  return text.length;
});

// Methods
const execCommand = (command: string, value?: string) => {
  if (props.disabled) return;
  
  document.execCommand(command, false, value);
  editor.value?.focus();
  handleInput();
};

const isActive = (command: string): boolean => {
  try {
    return document.queryCommandState(command);
  } catch {
    return false;
  }
};

const insertLink = () => {
  if (props.disabled) return;
  
  const url = prompt('Enter URL:');
  if (url) {
    execCommand('createLink', url);
  }
};

const handleInput = () => {
  if (!editor.value) return;
  
  const newContent = editor.value.innerHTML;
  content.value = newContent;
  emit('update:modelValue', newContent);
  emit('change', newContent);
};

const handlePaste = (event: ClipboardEvent) => {
  if (props.disabled) return;
  
  event.preventDefault();
  const text = event.clipboardData?.getData('text/plain') || '';
  document.execCommand('insertText', false, text);
};

const handleKeydown = (event: KeyboardEvent) => {
  if (props.disabled) return;
  
  // Handle max length
  if (props.maxLength && characterCount.value >= props.maxLength) {
    if (event.key.length === 1 && !event.ctrlKey && !event.metaKey) {
      event.preventDefault();
    }
  }
  
  // Handle shortcuts
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'b':
        event.preventDefault();
        execCommand('bold');
        break;
      case 'i':
        event.preventDefault();
        execCommand('italic');
        break;
      case 'u':
        event.preventDefault();
        execCommand('underline');
        break;
    }
  }
};

const handleFocus = (event: FocusEvent) => {
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  emit('blur', event);
};

const setContent = (newContent: string) => {
  if (editor.value) {
    editor.value.innerHTML = newContent;
    content.value = newContent;
  }
};

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value) {
    setContent(newValue);
  }
});

// Initialize
onMounted(() => {
  if (props.modelValue) {
    nextTick(() => {
      setContent(props.modelValue);
    });
  }
  
  // Set placeholder
  if (!props.modelValue && props.placeholder) {
    if (editor.value) {
      editor.value.setAttribute('data-placeholder', props.placeholder);
    }
  }
});
</script>

<style scoped>
/* Rich text editor styles */
.admin-rich-text-editor [contenteditable]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  pointer-events: none;
}

.admin-rich-text-editor [contenteditable] {
  outline: none;
}

.admin-rich-text-editor [contenteditable] p {
  margin: 0.5rem 0;
}

.admin-rich-text-editor [contenteditable] ul,
.admin-rich-text-editor [contenteditable] ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.admin-rich-text-editor [contenteditable] a {
  color: #3b82f6;
  text-decoration: underline;
}

.admin-rich-text-editor [contenteditable] strong {
  font-weight: bold;
}

.admin-rich-text-editor [contenteditable] em {
  font-style: italic;
}

.admin-rich-text-editor [contenteditable] u {
  text-decoration: underline;
}
</style>

<template>
  <AdminListTemplate
    title="Customers"
    subtitle="Manage customer information and relationships"
    :loading="loading"
    :error="error"
    :items="customers"
    :columns="columns"
    :show-create-button="true"
    create-button-text="Add Customer"
    create-route="/admin-spa/sales/customers/create"
    empty-state-title="No customers found"
    empty-state-message="Customer profiles will appear here when users register or make purchases."
    :show-pagination="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
  >
    <template #actions>
      <div class="flex space-x-3">
        <Button variant="outline" size="sm" @click="exportCustomers">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"/>
          </svg>
          Export
        </Button>
        <Button variant="outline" size="sm" @click="importCustomers">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
          </svg>
          Import
        </Button>
        <Button variant="primary" size="sm" @click="handleCreate">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Add Customer
        </Button>
      </div>
    </template>

    <template #filters>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Select
          v-model="filters.status"
          placeholder="Filter by status"
          :options="statusOptions"
          @change="applyFilters"
        />
        <Select
          v-model="filters.segment"
          placeholder="Customer segment"
          :options="segmentOptions"
          @change="applyFilters"
        />
        <Input
          v-model="filters.search"
          placeholder="Search customers..."
          @input="applyFilters"
        />
        <Select
          v-model="filters.dateRange"
          placeholder="Registration date"
          :options="dateRangeOptions"
          @change="applyFilters"
        />
      </div>
    </template>

    <template #item-actions="{ item }">
      <div class="flex space-x-2">
        <Button variant="ghost" size="sm" @click="viewCustomer(item)">
          View
        </Button>
        <Button variant="ghost" size="sm" @click="editCustomer(item)">
          Edit
        </Button>
        <Button variant="ghost" size="sm" @click="viewOrders(item)">
          Orders
        </Button>
        <Button 
          v-if="item.status === 'active'" 
          variant="ghost" 
          size="sm" 
          @click="deactivateCustomer(item)"
        >
          Deactivate
        </Button>
      </div>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Select, Input } from '@/components/ui';

// Router
const router = useRouter();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const currentPage = ref(1);
const totalPages = ref(8);
const totalItems = ref(156);

// Filters
const filters = ref({
  status: '',
  segment: '',
  search: '',
  dateRange: ''
});

// Options
const statusOptions = [
  { value: '', label: 'All Statuses' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'suspended', label: 'Suspended' }
];

const segmentOptions = [
  { value: '', label: 'All Segments' },
  { value: 'vip', label: 'VIP' },
  { value: 'regular', label: 'Regular' },
  { value: 'new', label: 'New Customer' },
  { value: 'returning', label: 'Returning' }
];

const dateRangeOptions = [
  { value: '', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' }
];

// Table columns
const columns = [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'phone', label: 'Phone', sortable: false },
  { key: 'totalOrders', label: 'Orders', sortable: true },
  { key: 'totalSpent', label: 'Total Spent', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'registeredDate', label: 'Registered', sortable: true },
  { key: 'actions', label: 'Actions', sortable: false }
];

// Mock data
const allCustomers = ref([
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+****************',
    totalOrders: 12,
    totalSpent: 2499.99,
    status: 'active',
    segment: 'vip',
    registeredDate: new Date('2023-06-15'),
    lastOrderDate: new Date('2024-01-10')
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '+****************',
    totalOrders: 5,
    totalSpent: 899.50,
    status: 'active',
    segment: 'regular',
    registeredDate: new Date('2023-09-22'),
    lastOrderDate: new Date('2024-01-08')
  },
  {
    id: 3,
    name: 'Bob Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    totalOrders: 1,
    totalSpent: 399.99,
    status: 'active',
    segment: 'new',
    registeredDate: new Date('2024-01-05'),
    lastOrderDate: new Date('2024-01-05')
  },
  {
    id: 4,
    name: 'Alice Brown',
    email: '<EMAIL>',
    phone: '+****************',
    totalOrders: 8,
    totalSpent: 1599.99,
    status: 'inactive',
    segment: 'returning',
    registeredDate: new Date('2023-03-10'),
    lastOrderDate: new Date('2023-11-20')
  },
  {
    id: 5,
    name: 'Charlie Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    totalOrders: 0,
    totalSpent: 0,
    status: 'suspended',
    segment: 'new',
    registeredDate: new Date('2024-01-01'),
    lastOrderDate: null
  }
]);

// Computed
const customers = computed(() => {
  let filtered = allCustomers.value;

  // Apply filters
  if (filters.value.status) {
    filtered = filtered.filter(customer => customer.status === filters.value.status);
  }
  if (filters.value.segment) {
    filtered = filtered.filter(customer => customer.segment === filters.value.segment);
  }
  if (filters.value.search) {
    const search = filters.value.search.toLowerCase();
    filtered = filtered.filter(customer => 
      customer.name.toLowerCase().includes(search) ||
      customer.email.toLowerCase().includes(search) ||
      customer.phone.includes(search)
    );
  }

  return filtered;
});

// Methods
const handleCreate = () => {
  router.push('/admin-spa/sales/customers/create');
};

const handleSearch = (query: string) => {
  filters.value.search = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  console.log('Sorting by:', column, order);
  // Implement sorting logic
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  // Load data for new page
};

const applyFilters = () => {
  currentPage.value = 1;
  // Apply filters and reload data
};

const exportCustomers = () => {
  console.log('Exporting customers...');
};

const importCustomers = () => {
  console.log('Importing customers...');
};

const viewCustomer = (customer: any) => {
  router.push(`/admin-spa/sales/customers/${customer.id}`);
};

const editCustomer = (customer: any) => {
  router.push(`/admin-spa/sales/customers/${customer.id}/edit`);
};

const viewOrders = (customer: any) => {
  router.push(`/admin-spa/sales/orders?customer=${customer.id}`);
};

const deactivateCustomer = (customer: any) => {
  console.log('Deactivating customer:', customer.name);
  // Implement customer deactivation
};

// Lifecycle
onMounted(() => {
  // Load customers data
});
</script>

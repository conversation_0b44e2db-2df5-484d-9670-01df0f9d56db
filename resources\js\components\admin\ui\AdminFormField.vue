<template>
  <div class="admin-form-field">
    <!-- Label -->
    <label
      v-if="label"
      :for="fieldId"
      :class="[
        'block text-sm font-medium mb-2',
        error ? 'text-red-700' : 'text-gray-700',
        required ? 'after:content-[\'*\'] after:text-red-500 after:ml-1' : ''
      ]"
    >
      {{ label }}
    </label>

    <!-- Help Text (above field) -->
    <p v-if="helpText && helpTextPosition === 'above'" class="text-sm text-gray-500 mb-2">
      {{ helpText }}
    </p>

    <!-- Field Container -->
    <div class="relative">
      <!-- Text Input -->
      <Input
        v-if="isTextInput"
        :id="fieldId"
        v-model="inputValue"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :class="fieldClasses"
        :min="min"
        :max="max"
        :step="step"
        :pattern="pattern"
        :autocomplete="autocomplete"
        :maxlength="maxLength"
        @blur="handleBlur"
        @input="handleInput"
        @focus="handleFocus"
      />

      <!-- Textarea -->
      <textarea
        v-else-if="type === 'textarea'"
        :id="fieldId"
        v-model="inputValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :rows="rows"
        :maxlength="maxLength"
        :class="[fieldClasses, 'resize-y']"
        @blur="handleBlur"
        @input="handleInput"
        @focus="handleFocus"
      />

      <!-- Select -->
      <Select
        v-else-if="type === 'select'"
        :id="fieldId"
        v-model="inputValue"
        :options="options"
        :placeholder="placeholder"
        :disabled="disabled"
        :required="required"
        :class="fieldClasses"
        @change="handleChange"
        @focus="handleFocus"
      />

      <!-- Checkbox -->
      <div v-else-if="type === 'checkbox'" class="flex items-center">
        <input
          :id="fieldId"
          v-model="inputValue"
          type="checkbox"
          :disabled="disabled"
          :required="required"
          :class="[
            'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded',
            error ? 'border-red-300' : ''
          ]"
          @change="handleChange"
          @focus="handleFocus"
        />
        <label v-if="checkboxLabel" :for="fieldId" class="ml-2 text-sm text-gray-700">
          {{ checkboxLabel }}
        </label>
      </div>

      <!-- Radio Group -->
      <div v-else-if="type === 'radio'" class="space-y-2">
        <div v-for="option in options" :key="option.value" class="flex items-center">
          <input
            :id="`${fieldId}-${option.value}`"
            v-model="inputValue"
            type="radio"
            :value="option.value"
            :name="name"
            :disabled="disabled"
            :required="required"
            :class="[
              'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300',
              error ? 'border-red-300' : ''
            ]"
            @change="handleChange"
            @focus="handleFocus"
          />
          <label :for="`${fieldId}-${option.value}`" class="ml-2 text-sm text-gray-700">
            {{ option.label }}
          </label>
        </div>
      </div>

      <!-- File Input -->
      <div v-else-if="type === 'file'" class="space-y-2">
        <input
          :id="fieldId"
          type="file"
          :multiple="multiple"
          :accept="accept"
          :disabled="disabled"
          :required="required"
          :class="[
            'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100',
            error ? 'border-red-300' : 'border-gray-300'
          ]"
          @change="handleFileChange"
          @focus="handleFocus"
        />
        <div v-if="filePreview.length > 0" class="space-y-1">
          <div v-for="file in filePreview" :key="file.name" class="flex items-center justify-between text-sm text-gray-600 bg-gray-50 p-2 rounded">
            <span>{{ file.name }} ({{ formatFileSize(file.size) }})</span>
            <Button variant="ghost" size="sm" @click="removeFile(file)" class="text-red-600 hover:text-red-700">
              Remove
            </Button>
          </div>
        </div>
      </div>

      <!-- Date Picker -->
      <Input
        v-else-if="isDateInput"
        :id="fieldId"
        v-model="inputValue"
        :type="type"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :class="fieldClasses"
        :min="min"
        :max="max"
        @blur="handleBlur"
        @input="handleInput"
        @focus="handleFocus"
      />

      <!-- Loading Indicator -->
      <div v-if="loading" class="absolute inset-y-0 right-0 pr-3 flex items-center">
        <Loading size="sm" />
      </div>

      <!-- Clear Button -->
      <button
        v-if="clearable && inputValue && !disabled && !readonly"
        type="button"
        class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
        @click="clearValue"
      >
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Character Count -->
    <div v-if="showCharCount && maxLength" class="mt-1 text-right">
      <span :class="[
        'text-xs',
        inputValue?.length > maxLength * 0.9 ? 'text-red-500' : 'text-gray-500'
      ]">
        {{ inputValue?.length || 0 }}/{{ maxLength }}
      </span>
    </div>

    <!-- Error Message -->
    <p v-if="error" class="mt-1 text-sm text-red-600">
      {{ error }}
    </p>

    <!-- Help Text (below field) -->
    <p v-if="helpText && helpTextPosition !== 'above'" class="mt-1 text-sm text-gray-500">
      {{ helpText }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { Input, Select, Button, Loading } from '@/components/ui';

interface Option {
  label: string;
  value: any;
}

interface Props {
  modelValue?: any;
  name?: string;
  label?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search' | 'date' | 'time' | 'datetime-local' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file';
  placeholder?: string;
  helpText?: string;
  helpTextPosition?: 'above' | 'below';
  error?: string;
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  clearable?: boolean;
  loading?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  min?: string | number;
  max?: string | number;
  step?: string | number;
  pattern?: string;
  autocomplete?: string;
  maxLength?: number;
  showCharCount?: boolean;
  rows?: number;
  options?: Option[];
  checkboxLabel?: string;
  multiple?: boolean;
  accept?: string;
  validateOnBlur?: boolean;
  validateOnInput?: boolean;
  validator?: (value: any) => string | null;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  helpTextPosition: 'below',
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
  loading: false,
  size: 'md',
  variant: 'default',
  showCharCount: false,
  rows: 3,
  options: () => [],
  multiple: false,
  accept: '*',
  validateOnBlur: true,
  validateOnInput: false
});

const emit = defineEmits<{
  'update:modelValue': [value: any];
  blur: [event: Event];
  focus: [event: Event];
  change: [value: any];
  'file-change': [files: FileList | null];
}>();

// State
const inputValue = ref(props.modelValue);
const filePreview = ref<File[]>([]);
const fieldId = computed(() => props.name || `field-${Math.random().toString(36).substr(2, 9)}`);

// Computed
const isTextInput = computed(() => {
  return ['text', 'email', 'password', 'number', 'tel', 'url', 'search'].includes(props.type);
});

const isDateInput = computed(() => {
  return ['date', 'time', 'datetime-local'].includes(props.type);
});

const fieldClasses = computed(() => {
  const baseClasses = 'block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500';
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };
  const errorClasses = props.error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : '';
  
  return `${baseClasses} ${sizeClasses[props.size]} ${errorClasses}`;
});

// Methods
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement;
  inputValue.value = target.value;
  emit('update:modelValue', inputValue.value);
  
  if (props.validateOnInput && props.validator) {
    const error = props.validator(inputValue.value);
    // Handle validation error
  }
};

const handleChange = (value: any) => {
  inputValue.value = value;
  emit('update:modelValue', inputValue.value);
  emit('change', value);
};

const handleBlur = (event: Event) => {
  emit('blur', event);
  
  if (props.validateOnBlur && props.validator) {
    const error = props.validator(inputValue.value);
    // Handle validation error
  }
};

const handleFocus = (event: Event) => {
  emit('focus', event);
};

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  
  if (files) {
    filePreview.value = Array.from(files);
    emit('file-change', files);
    emit('update:modelValue', files);
  }
};

const removeFile = (fileToRemove: File) => {
  filePreview.value = filePreview.value.filter(file => file !== fileToRemove);
  // Update the actual file input would require more complex logic
};

const clearValue = () => {
  inputValue.value = '';
  emit('update:modelValue', '');
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue;
});
</script>

<style scoped>
/* Custom styles for AdminFormField */
.admin-form-field {
  @apply w-full;
}

/* File input styling */
.admin-form-field input[type="file"] {
  @apply cursor-pointer;
}

/* Focus styles */
.admin-form-field :focus {
  @apply outline-none ring-2 ring-blue-500 ring-opacity-50;
}

/* Error state styles */
.admin-form-field.has-error input,
.admin-form-field.has-error textarea,
.admin-form-field.has-error select {
  @apply border-red-300 focus:border-red-500 focus:ring-red-500;
}

/* Disabled state */
.admin-form-field input:disabled,
.admin-form-field textarea:disabled,
.admin-form-field select:disabled {
  @apply bg-gray-50 text-gray-500 cursor-not-allowed;
}
</style>
